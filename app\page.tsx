import Image from 'next/image';
import Link from 'next/link';
import { connectToDatabase } from '@/lib/mongodb';
import Product from '@/models/Product';

export default async function HomePage() {
  await connectToDatabase();
  
  // Get featured products
  const featuredProducts = await Product.find({ featured: true }).limit(4);
  
  return (
    <div>
      {/* Hero Section */}
      <section className="relative h-[80vh] bg-vintage">
        <div className="absolute inset-0">
          <Image
            src="/images/hero-vintage.jpg"
            alt="Vintage Fashion Collection"
            fill
            className="object-cover object-center opacity-90"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-r from-sepia-900/70 to-transparent"></div>
        </div>
        
        <div className="relative container mx-auto px-4 h-full flex items-center">
          <div className="max-w-lg text-white">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-4">
              Timeless Style Rediscovered
            </h1>
            <p className="text-lg md:text-xl mb-8">
              Curated vintage fashion pieces from the 1920s to the 1990s, each with its own unique story and character.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Link href="/shop" className="btn-primary text-center">
                Shop Collection
              </Link>
              <Link href="/about" className="btn-secondary text-center">
                Our Story
              </Link>
            </div>
          </div>
        </div>
      </section>
      
      {/* Era Categories */}
      <section className="py-16 bg-cream-100">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">Shop by Era</h2>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {['1950s', '1960s', '1970s', '1980s'].map((era) => (
              <Link 
                key={era} 
                href={`/shop?era=${era.toLowerCase()}`}
                className="group relative h-64 overflow-hidden"
              >
                <Image
                  src={`/images/era-${era.toLowerCase()}.jpg`}
                  alt={`${era} Fashion`}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-500"
                />
                <div className="absolute inset-0 bg-sepia-900/30 group-hover:bg-sepia-900/20 transition-colors"></div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <h3 className="text-2xl font-bold text-white bg-sepia-900/60 px-6 py-2">
                    {era}
                  </h3>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>
      
      {/* Featured Products */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-4">Featured Treasures</h2>
          <p className="text-center text-sepia-800 mb-12 max-w-2xl mx-auto">
            Our carefully selected pieces that represent the best of vintage fashion, quality, and style.
          </p>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {featuredProducts.map((product) => (
              <Link 
                key={product._id.toString()} 
                href={`/shop/products/${product._id}`}
                className="vintage-card group"
              >
                <div className="relative h-80 overflow-hidden">
                  {product.images && product.images[0] ? (
                    <Image
                      src={product.images[0]}
                      alt={product.name}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-500"
                    />
                  ) : (
                    <div className="w-full h-full bg-gray-200"></div>
                  )}
                  {product.inventory <= 3 && product.inventory > 0 && (
                    <div className="absolute top-2 right-2 bg-rust-600 text-white text-xs px-2 py-1">
                      Only {product.inventory} left
                    </div>
                  )}
                </div>
                <div className="p-4">
                  <h3 className="font-medium text-lg">{product.name}</h3>
                  <p className="text-rust-800 font-semibold mt-1">${product.price.toFixed(2)}</p>
                  <div className="mt-2 text-sm text-sepia-700">{product.category}</div>
                </div>
              </Link>
            ))}
          </div>
          
          <div className="text-center mt-12">
            <Link href="/shop" className="btn-secondary">
              View All Products
            </Link>
          </div>
        </div>
      </section>
      
      {/* Story/About Section */}
      <section className="py-16 bg-vintage">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center gap-12">
            <div className="md:w-1/2">
              <div className="relative h-96 w-full">
                <Image
                  src="/images/about-vintage.jpg"
                  alt="Our Vintage Collection Process"
                  fill
                  className="object-cover rounded-sm"
                />
              </div>
            </div>
            
            <div className="md:w-1/2">
              <h2 className="text-3xl font-bold mb-4">Our Vintage Journey</h2>
              <div className="vintage-divider w-24 mb-6"></div>
              <p className="text-sepia-800 mb-4">
                Each piece in our collection has been carefully sourced from around the world, 
                with a focus on quality, uniqueness, and historical significance.
              </p>
              <p className="text-sepia-800 mb-6">
                We believe in sustainable fashion through the celebration of vintage pieces 
                that have stood the test of time. By giving these garments a second life, 
                we&apos;re reducing waste and preserving fashion history.
              </p>
              <Link href="/about" className="btn-primary inline-block">
                Read Our Story
              </Link>
            </div>
          </div>
        </div>
      </section>
      
      {/* Newsletter */}
      <section className="py-16 bg-sage-100">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">Join Our Vintage Community</h2>
          <p className="text-sepia-800 mb-8 max-w-2xl mx-auto">
            Subscribe to our newsletter for early access to new arrivals, styling tips, 
            and exclusive vintage fashion insights.
          </p>
          
          <form className="max-w-md mx-auto flex">
            <input 
              type="email" 
              placeholder="Your email address" 
              className="vintage-input flex-grow"
              required
            />
            <button 
              type="submit" 
              className="bg-rust-600 text-white px-6 py-2 hover:bg-rust-700"
            >
              Subscribe
            </button>
          </form>
        </div>
      </section>
    </div>
  );
}