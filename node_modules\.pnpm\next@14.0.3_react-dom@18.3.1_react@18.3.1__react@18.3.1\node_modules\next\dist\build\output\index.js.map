{"version": 3, "sources": ["../../../src/build/output/index.ts"], "names": ["startedDevelopmentServer", "formatAmpMessages", "ampValidation", "watchCompilers", "reportTrigger", "appUrl", "bindAddr", "consoleStore", "setState", "amp", "output", "bold", "messages", "chalkError", "red", "ampError", "page", "error", "push", "message", "specUrl", "chalk<PERSON>arn", "yellow", "ampWarn", "warn", "errors", "warnings", "dev<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "err", "code", "filter", "length", "index", "textTable", "align", "stringLength", "str", "stripAnsi", "buildStore", "createStore", "client", "server", "edgeServer", "buildWasDone", "clientWasLoading", "serverWasLoading", "edgeServerWasLoading", "subscribe", "state", "trigger", "getState", "loading", "bootstrap", "partialState", "typeChecking", "totalModulesCount", "hasEdgeServer", "concat", "Object", "keys", "k", "sort", "reduce", "a", "c", "newAmp", "tapCompiler", "key", "compiler", "onEvent", "hooks", "invalid", "tap", "done", "stats", "formatWebpackMessages", "to<PERSON><PERSON>", "preset", "moduleTrace", "hasErrors", "hasWarnings", "compilation", "modules", "size", "COMPILER_NAMES", "status", "undefined", "internalSegments", "segment", "includes", "replace", "endsWith", "slice"], "mappings": ";;;;;;;;;;;;;;;;;;IAWgBA,wBAAwB;eAAxBA;;IAkCAC,iBAAiB;eAAjBA;;IAuJAC,aAAa;eAAbA;;IA0BAC,cAAc;eAAdA;;IAiGAC,aAAa;eAAbA;;;4BA/TkB;kEACZ;kEACA;iEACE;8EACU;uBACI;2BAGP;;;;;;AAGxB,SAASJ,yBAAyBK,MAAc,EAAEC,QAAgB;IACvEC,YAAY,CAACC,QAAQ,CAAC;QAAEH;QAAQC;IAAS;AAC3C;AAgCO,SAASL,kBAAkBQ,GAAkB;IAClD,IAAIC,SAASC,IAAAA,gBAAI,EAAC,oBAAoB;IACtC,IAAIC,WAAuB,EAAE;IAE7B,MAAMC,aAAaC,IAAAA,eAAG,EAAC;IACvB,SAASC,SAASC,IAAY,EAAEC,KAAgB;QAC9CL,SAASM,IAAI,CAAC;YAACF;YAAMH;YAAYI,MAAME,OAAO;YAAEF,MAAMG,OAAO,IAAI;SAAG;IACtE;IAEA,MAAMC,YAAYC,IAAAA,kBAAM,EAAC;IACzB,SAASC,QAAQP,IAAY,EAAEQ,IAAe;QAC5CZ,SAASM,IAAI,CAAC;YAACF;YAAMK;YAAWG,KAAKL,OAAO;YAAEK,KAAKJ,OAAO,IAAI;SAAG;IACnE;IAEA,IAAK,MAAMJ,QAAQP,IAAK;QACtB,IAAI,EAAEgB,MAAM,EAAEC,QAAQ,EAAE,GAAGjB,GAAG,CAACO,KAAK;QAEpC,MAAMW,gBAAgB,CAACC,MAAmBA,IAAIC,IAAI,KAAK;QACvDJ,SAASA,OAAOK,MAAM,CAACH;QACvBD,WAAWA,SAASI,MAAM,CAACH;QAC3B,IAAI,CAAEF,CAAAA,OAAOM,MAAM,IAAIL,SAASK,MAAM,AAAD,GAAI;YAEvC;QACF;QAEA,IAAIN,OAAOM,MAAM,EAAE;YACjBhB,SAASC,MAAMS,MAAM,CAAC,EAAE;YACxB,IAAK,IAAIO,QAAQ,GAAGA,QAAQP,OAAOM,MAAM,EAAE,EAAEC,MAAO;gBAClDjB,SAAS,IAAIU,MAAM,CAACO,MAAM;YAC5B;QACF;QACA,IAAIN,SAASK,MAAM,EAAE;YACnBR,QAAQE,OAAOM,MAAM,GAAG,KAAKf,MAAMU,QAAQ,CAAC,EAAE;YAC9C,IAAK,IAAIM,QAAQ,GAAGA,QAAQN,SAASK,MAAM,EAAE,EAAEC,MAAO;gBACpDT,QAAQ,IAAIG,QAAQ,CAACM,MAAM;YAC7B;QACF;QACApB,SAASM,IAAI,CAAC;YAAC;YAAI;YAAI;YAAI;SAAG;IAChC;IAEA,IAAI,CAACN,SAASmB,MAAM,EAAE;QACpB,OAAO;IACT;IAEArB,UAAUuB,IAAAA,kBAAS,EAACrB,UAAU;QAC5BsB,OAAO;YAAC;YAAK;YAAK;YAAK;SAAI;QAC3BC,cAAaC,GAAW;YACtB,OAAOC,IAAAA,kBAAS,EAACD,KAAKL,MAAM;QAC9B;IACF;IAEA,OAAOrB;AACT;AAEA,MAAM4B,aAAaC,IAAAA,iBAAW,EAAmB;IAC/C,iCAAiC;IACjCC,QAAQ,CAAC;IACT,iCAAiC;IACjCC,QAAQ,CAAC;IACT,iCAAiC;IACjCC,YAAY,CAAC;AACf;AACA,IAAIC,eAAe;AACnB,IAAIC,mBAAmB;AACvB,IAAIC,mBAAmB;AACvB,IAAIC,uBAAuB;AAE3BR,WAAWS,SAAS,CAAC,CAACC;IACpB,MAAM,EAAEvC,GAAG,EAAE+B,MAAM,EAAEC,MAAM,EAAEC,UAAU,EAAEO,OAAO,EAAE,GAAGD;IAErD,MAAM,EAAE3C,MAAM,EAAE,GAAGE,YAAY,CAAC2C,QAAQ;IAExC,IAAIV,OAAOW,OAAO,IAAIV,OAAOU,OAAO,KAAIT,8BAAAA,WAAYS,OAAO,GAAE;QAC3D5C,YAAY,CAACC,QAAQ,CACnB;YACE4C,WAAW;YACX/C,QAAQA;YACR,wEAAwE;YACxE8C,SAAS;YACTF;QACF,GACA;QAEFL,mBAAmB,AAAC,CAACD,gBAAgBC,oBAAqBJ,OAAOW,OAAO;QACxEN,mBAAmB,AAAC,CAACF,gBAAgBE,oBAAqBJ,OAAOU,OAAO;QACxEL,uBACE,AAAC,CAACH,gBAAgBG,wBAAyBJ,WAAWS,OAAO;QAC/DR,eAAe;QACf;IACF;IAEAA,eAAe;IAEf,IAAIU,eAAqC;QACvCD,WAAW;QACX/C,QAAQA;QACR8C,SAAS;QACTG,cAAc;QACdC,mBACE,AAACX,CAAAA,mBAAmBJ,OAAOe,iBAAiB,GAAG,CAAA,IAC9CV,CAAAA,mBAAmBJ,OAAOc,iBAAiB,GAAG,CAAA,IAC9CT,CAAAA,uBAAuBJ,CAAAA,8BAAAA,WAAYa,iBAAiB,KAAI,IAAI,CAAA;QAC/DC,eAAe,CAAC,CAACd;IACnB;IACA,IAAIF,OAAOf,MAAM,IAAImB,kBAAkB;QACrC,0BAA0B;QAC1BrC,YAAY,CAACC,QAAQ,CACnB;YACE,GAAG6C,YAAY;YACf5B,QAAQe,OAAOf,MAAM;YACrBC,UAAU;QACZ,GACA;IAEJ,OAAO,IAAIe,OAAOhB,MAAM,IAAIoB,kBAAkB;QAC5CtC,YAAY,CAACC,QAAQ,CACnB;YACE,GAAG6C,YAAY;YACf5B,QAAQgB,OAAOhB,MAAM;YACrBC,UAAU;QACZ,GACA;IAEJ,OAAO,IAAIgB,WAAWjB,MAAM,IAAIqB,sBAAsB;QACpDvC,YAAY,CAACC,QAAQ,CACnB;YACE,GAAG6C,YAAY;YACf5B,QAAQiB,WAAWjB,MAAM;YACzBC,UAAU;QACZ,GACA;IAEJ,OAAO;QACL,iCAAiC;QACjC,MAAMA,WAAW;eACXc,OAAOd,QAAQ,IAAI,EAAE;eACrBe,OAAOf,QAAQ,IAAI,EAAE;eACrBgB,WAAWhB,QAAQ,IAAI,EAAE;SAC9B,CAAC+B,MAAM,CAACxD,kBAAkBQ,QAAQ,EAAE;QAErCF,YAAY,CAACC,QAAQ,CACnB;YACE,GAAG6C,YAAY;YACf5B,QAAQ;YACRC,UAAUA,SAASK,MAAM,KAAK,IAAI,OAAOL;QAC3C,GACA;IAEJ;AACF;AAEO,SAASxB,cACdc,IAAY,EACZS,MAAmB,EACnBC,QAAqB;IAErB,MAAM,EAAEjB,GAAG,EAAE,GAAG6B,WAAWY,QAAQ;IACnC,IAAI,CAAEzB,CAAAA,OAAOM,MAAM,IAAIL,SAASK,MAAM,AAAD,GAAI;QACvCO,WAAW9B,QAAQ,CAAC;YAClBC,KAAKiD,OAAOC,IAAI,CAAClD,KACdqB,MAAM,CAAC,CAAC8B,IAAMA,MAAM5C,MACpB6C,IAAI,EACL,wCAAwC;aACvCC,MAAM,CAAC,CAACC,GAAGC,IAAO,CAAA,AAACD,CAAC,CAACC,EAAE,GAAGvD,GAAG,CAACuD,EAAE,EAAGD,CAAAA,GAAI,CAAC;QAC7C;QACA;IACF;IAEA,MAAME,SAAwB;QAAE,GAAGxD,GAAG;QAAE,CAACO,KAAK,EAAE;YAAES;YAAQC;QAAS;IAAE;IACrEY,WAAW9B,QAAQ,CAAC;QAClBC,KAAKiD,OAAOC,IAAI,CAACM,QACdJ,IAAI,EACL,wCAAwC;SACvCC,MAAM,CAAC,CAACC,GAAGC,IAAO,CAAA,AAACD,CAAC,CAACC,EAAE,GAAGC,MAAM,CAACD,EAAE,EAAGD,CAAAA,GAAI,CAAC;IAChD;AACF;AAEO,SAAS5D,eACdqC,MAAwB,EACxBC,MAAwB,EACxBC,UAA4B;IAE5BJ,WAAW9B,QAAQ,CAAC;QAClBgC,QAAQ;YAAEW,SAAS;QAAK;QACxBV,QAAQ;YAAEU,SAAS;QAAK;QACxBT,YAAY;YAAES,SAAS;QAAK;QAC5BF,SAAS;IACX;IAEA,SAASiB,YACPC,GAAuB,EACvBC,QAA0B,EAC1BC,OAAwC;QAExCD,SAASE,KAAK,CAACC,OAAO,CAACC,GAAG,CAAC,CAAC,cAAc,EAAEL,IAAI,CAAC,EAAE;YACjDE,QAAQ;gBAAElB,SAAS;YAAK;QAC1B;QAEAiB,SAASE,KAAK,CAACG,IAAI,CAACD,GAAG,CAAC,CAAC,WAAW,EAAEL,IAAI,CAAC,EAAE,CAACO;YAC5CpC,WAAW9B,QAAQ,CAAC;gBAAEC,KAAK,CAAC;YAAE;YAE9B,MAAM,EAAEgB,MAAM,EAAEC,QAAQ,EAAE,GAAGiD,IAAAA,8BAAqB,EAChDD,MAAME,MAAM,CAAC;gBACXC,QAAQ;gBACRC,aAAa;YACf;YAGF,MAAMC,YAAY,CAAC,EAACtD,0BAAAA,OAAQM,MAAM;YAClC,MAAMiD,cAAc,CAAC,EAACtD,4BAAAA,SAAUK,MAAM;YAEtCsC,QAAQ;gBACNlB,SAAS;gBACTI,mBAAmBmB,MAAMO,WAAW,CAACC,OAAO,CAACC,IAAI;gBACjD1D,QAAQsD,YAAYtD,SAAS;gBAC7BC,UAAUsD,cAActD,WAAW;YACrC;QACF;IACF;IAEAwC,YAAYkB,yBAAc,CAAC5C,MAAM,EAAEA,QAAQ,CAAC6C;QAC1C,IACE,CAACA,OAAOlC,OAAO,IACf,CAACb,WAAWY,QAAQ,GAAGT,MAAM,CAACU,OAAO,IACrC,CAACb,WAAWY,QAAQ,GAAGR,UAAU,CAACS,OAAO,IACzCkC,OAAO9B,iBAAiB,GAAG,GAC3B;YACAjB,WAAW9B,QAAQ,CAAC;gBAClBgC,QAAQ6C;gBACRpC,SAASqC;YACX;QACF,OAAO;YACLhD,WAAW9B,QAAQ,CAAC;gBAClBgC,QAAQ6C;YACV;QACF;IACF;IACAnB,YAAYkB,yBAAc,CAAC3C,MAAM,EAAEA,QAAQ,CAAC4C;QAC1C,IACE,CAACA,OAAOlC,OAAO,IACf,CAACb,WAAWY,QAAQ,GAAGV,MAAM,CAACW,OAAO,IACrC,CAACb,WAAWY,QAAQ,GAAGR,UAAU,CAACS,OAAO,IACzCkC,OAAO9B,iBAAiB,GAAG,GAC3B;YACAjB,WAAW9B,QAAQ,CAAC;gBAClBiC,QAAQ4C;gBACRpC,SAASqC;YACX;QACF,OAAO;YACLhD,WAAW9B,QAAQ,CAAC;gBAClBiC,QAAQ4C;YACV;QACF;IACF;IACAnB,YAAYkB,yBAAc,CAAC1C,UAAU,EAAEA,YAAY,CAAC2C;QAClD,IACE,CAACA,OAAOlC,OAAO,IACf,CAACb,WAAWY,QAAQ,GAAGV,MAAM,CAACW,OAAO,IACrC,CAACb,WAAWY,QAAQ,GAAGT,MAAM,CAACU,OAAO,IACrCkC,OAAO9B,iBAAiB,GAAG,GAC3B;YACAjB,WAAW9B,QAAQ,CAAC;gBAClBkC,YAAY2C;gBACZpC,SAASqC;YACX;QACF,OAAO;YACLhD,WAAW9B,QAAQ,CAAC;gBAClBkC,YAAY2C;YACd;QACF;IACF;AACF;AAEA,MAAME,mBAAmB;IAAC;IAA0B;CAAoB;AACjE,SAASnF,cAAc6C,OAAe;IAC3C,KAAK,MAAMuC,WAAWD,iBAAkB;QACtC,IAAItC,QAAQwC,QAAQ,CAACD,UAAU;YAC7BvC,UAAUA,QAAQyC,OAAO,CAACF,SAAS;QACrC;IACF;IAEA,IAAIvC,QAAQlB,MAAM,GAAG,KAAKkB,QAAQ0C,QAAQ,CAAC,MAAM;QAC/C1C,UAAUA,QAAQ2C,KAAK,CAAC,GAAG,CAAC;IAC9B;IAEAtD,WAAW9B,QAAQ,CAAC;QAClByC;IACF;AACF"}