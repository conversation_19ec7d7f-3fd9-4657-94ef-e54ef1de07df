'use client';

import Link from 'next/link';
import { useState } from 'react';
import { usePathname } from 'next/navigation';
import { ShoppingBag, User, Menu, X, Search } from 'lucide-react';

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const pathname = usePathname();

  const isActive = (path: string) => pathname === path;

  const navLinks = [
    { name: 'Home', path: '/' },
    { name: 'Shop', path: '/shop' },
    { name: 'Collections', path: '/collections' },
    { name: 'About', path: '/about' },
    { name: 'Contact', path: '/contact' },
  ];

  return (
    <header className="backdrop-blur-md bg-white/70 border-b border-sepia-200 shadow-sm sticky top-0 z-50">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between py-3">
          {/* Logo */}
          <Link href="/" className="flex items-center gap-2">
            <span className="inline-block w-8 h-8 rounded-full bg-gradient-to-tr from-rust-600 to-sage-400 flex items-center justify-center text-white font-bold text-xl shadow-md">VT</span>
            <span className="hidden sm:inline text-2xl font-playfair font-bold text-sepia-900 tracking-tight">Vintage Treasures</span>
          </Link>

          {/* Navigation */}
          <nav className="hidden md:flex gap-2">
            {navLinks.map((link) => (
              <Link
                key={link.path}
                href={link.path}
                className={`px-4 py-2 rounded-full transition-colors font-medium text-lg ${
                  isActive(link.path)
                    ? 'bg-rust-600 text-white shadow'
                    : 'text-sepia-800 hover:bg-sage-200 hover:text-rust-700'
                }`}
              >
                {link.name}
              </Link>
            ))}
          </nav>

          {/* Icons */}
          <div className="flex items-center gap-3">
            <button className="text-sepia-800 hover:text-rust-700 p-2 rounded-full hover:bg-sage-100 transition-colors">
              <Search size={20} />
            </button>
            <Link href="/account" className="text-sepia-800 hover:text-rust-700 p-2 rounded-full hover:bg-sage-100 transition-colors">
              <User size={20} />
            </Link>
            <Link href="/cart" className="text-sepia-800 hover:text-rust-700 p-2 rounded-full hover:bg-sage-100 transition-colors relative">
              <ShoppingBag size={20} />
              <span className="absolute -top-1 -right-1 bg-rust-600 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center border-2 border-white">0</span>
            </Link>
            {/* Mobile menu button */}
            <button
              className="md:hidden ml-2 text-sepia-800 hover:text-rust-700 p-2 rounded-full hover:bg-sage-100 transition-colors"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <nav className="md:hidden mt-2 pb-4 animate-fade-in">
            <ul className="flex flex-col gap-2">
              {navLinks.map((link) => (
                <li key={link.path}>
                  <Link
                    href={link.path}
                    className={`block px-4 py-2 rounded-full font-medium text-lg transition-colors ${
                      isActive(link.path)
                        ? 'bg-rust-600 text-white shadow'
                        : 'text-sepia-800 hover:bg-sage-200 hover:text-rust-700'
                    }`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </nav>
        )}
      </div>
    </header>
  );
}