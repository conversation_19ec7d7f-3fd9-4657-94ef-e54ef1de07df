[{"C:\\Users\\<USER>\\Documents\\web-development\\fullstack\\2second\\app\\admin\\page.tsx": "1", "C:\\Users\\<USER>\\Documents\\web-development\\fullstack\\2second\\app\\admin\\products\\page.tsx": "2", "C:\\Users\\<USER>\\Documents\\web-development\\fullstack\\2second\\app\\api\\auth\\[...nextauth]\\route.ts": "3", "C:\\Users\\<USER>\\Documents\\web-development\\fullstack\\2second\\app\\layout.tsx": "4", "C:\\Users\\<USER>\\Documents\\web-development\\fullstack\\2second\\app\\page.tsx": "5", "C:\\Users\\<USER>\\Documents\\web-development\\fullstack\\2second\\app\\shop\\page.tsx": "6", "C:\\Users\\<USER>\\Documents\\web-development\\fullstack\\2second\\app\\shop\\products\\[id]\\page.tsx": "7", "C:\\Users\\<USER>\\Documents\\web-development\\fullstack\\2second\\app\\unauthorized\\page.tsx": "8", "C:\\Users\\<USER>\\Documents\\web-development\\fullstack\\2second\\components\\layout\\Footer.tsx": "9", "C:\\Users\\<USER>\\Documents\\web-development\\fullstack\\2second\\components\\layout\\Header.tsx": "10", "C:\\Users\\<USER>\\Documents\\web-development\\fullstack\\2second\\components\\shop\\ProductFilter.tsx": "11", "C:\\Users\\<USER>\\Documents\\web-development\\fullstack\\2second\\lib\\auth.ts": "12", "C:\\Users\\<USER>\\Documents\\web-development\\fullstack\\2second\\lib\\cloudinary-loader.ts": "13", "C:\\Users\\<USER>\\Documents\\web-development\\fullstack\\2second\\lib\\mongodb.ts": "14"}, {"size": 1966, "mtime": 1749643687310, "results": "15", "hashOfConfig": "16"}, {"size": 3166, "mtime": 1749632260313, "results": "17", "hashOfConfig": "16"}, {"size": 1607, "mtime": 1749643944224, "results": "18", "hashOfConfig": "16"}, {"size": 1056, "mtime": 1749632533288, "results": "19", "hashOfConfig": "16"}, {"size": 7512, "mtime": 1749644132214, "results": "20", "hashOfConfig": "16"}, {"size": 4376, "mtime": 1749643772365, "results": "21", "hashOfConfig": "16"}, {"size": 2963, "mtime": 1749632279871, "results": "22", "hashOfConfig": "16"}, {"size": 964, "mtime": 1749644143916, "results": "23", "hashOfConfig": "16"}, {"size": 4044, "mtime": 1749632553534, "results": "24", "hashOfConfig": "16"}, {"size": 3915, "mtime": 1749636894157, "results": "25", "hashOfConfig": "16"}, {"size": 3404, "mtime": 1749643856468, "results": "26", "hashOfConfig": "16"}, {"size": 1517, "mtime": 1749643588384, "results": "27", "hashOfConfig": "16"}, {"size": 342, "mtime": 1749633878533, "results": "28", "hashOfConfig": "16"}, {"size": 525, "mtime": 1749632109874, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "zyx1ur", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\web-development\\fullstack\\2second\\app\\admin\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\web-development\\fullstack\\2second\\app\\admin\\products\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\web-development\\fullstack\\2second\\app\\api\\auth\\[...nextauth]\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\web-development\\fullstack\\2second\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\web-development\\fullstack\\2second\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\web-development\\fullstack\\2second\\app\\shop\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\web-development\\fullstack\\2second\\app\\shop\\products\\[id]\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\web-development\\fullstack\\2second\\app\\unauthorized\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\web-development\\fullstack\\2second\\components\\layout\\Footer.tsx", [], [], "C:\\Users\\<USER>\\Documents\\web-development\\fullstack\\2second\\components\\layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\Documents\\web-development\\fullstack\\2second\\components\\shop\\ProductFilter.tsx", [], [], "C:\\Users\\<USER>\\Documents\\web-development\\fullstack\\2second\\lib\\auth.ts", [], [], "C:\\Users\\<USER>\\Documents\\web-development\\fullstack\\2second\\lib\\cloudinary-loader.ts", [], [], "C:\\Users\\<USER>\\Documents\\web-development\\fullstack\\2second\\lib\\mongodb.ts", [], []]