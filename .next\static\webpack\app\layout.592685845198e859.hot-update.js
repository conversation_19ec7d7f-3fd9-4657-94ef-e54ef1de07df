/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Ccomponents%5Clayout%5CHeader.tsx&modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Cnode_modules%5C.pnpm%5Cnext%4014.0.3_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Cnode_modules%5C.pnpm%5Cnext%4014.0.3_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Playfair_Display%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22display%22%3A%22swap%22%2C%22variable%22%3A%22--font-playfair%22%7D%5D%2C%22variableName%22%3A%22playfair%22%7D&modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Cnode_modules%5C.pnpm%5Cnext%4014.0.3_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Montserrat%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22display%22%3A%22swap%22%2C%22variable%22%3A%22--font-montserrat%22%7D%5D%2C%22variableName%22%3A%22montserrat%22%7D&modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Capp%5Cglobals.css&server=false!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Ccomponents%5Clayout%5CHeader.tsx&modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Cnode_modules%5C.pnpm%5Cnext%4014.0.3_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Cnode_modules%5C.pnpm%5Cnext%4014.0.3_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Playfair_Display%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22display%22%3A%22swap%22%2C%22variable%22%3A%22--font-playfair%22%7D%5D%2C%22variableName%22%3A%22playfair%22%7D&modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Cnode_modules%5C.pnpm%5Cnext%4014.0.3_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Montserrat%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22display%22%3A%22swap%22%2C%22variable%22%3A%22--font-montserrat%22%7D%5D%2C%22variableName%22%3A%22montserrat%22%7D&modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Capp%5Cglobals.css&server=false! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/layout/Header.tsx */ \"(app-pages-browser)/./components/layout/Header.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/link.js */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/link.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Playfair_Display\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-playfair\"}],\"variableName\":\"playfair\"} */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Playfair_Display\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\",\\\"variable\\\":\\\"--font-playfair\\\"}],\\\"variableName\\\":\\\"playfair\\\"}\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Montserrat\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-montserrat\"}],\"variableName\":\"montserrat\"} */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Montserrat\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\",\\\"variable\\\":\\\"--font-montserrat\\\"}],\\\"variableName\\\":\\\"montserrat\\\"}\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/globals.css */ \"(app-pages-browser)/./app/globals.css\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Ccomponents%5Clayout%5CHeader.tsx&modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Cnode_modules%5C.pnpm%5Cnext%4014.0.3_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Cnode_modules%5C.pnpm%5Cnext%4014.0.3_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Playfair_Display%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22display%22%3A%22swap%22%2C%22variable%22%3A%22--font-playfair%22%7D%5D%2C%22variableName%22%3A%22playfair%22%7D&modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Cnode_modules%5C.pnpm%5Cnext%4014.0.3_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Montserrat%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22display%22%3A%22swap%22%2C%22variable%22%3A%22--font-montserrat%22%7D%5D%2C%22variableName%22%3A%22montserrat%22%7D&modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Capp%5Cglobals.css&server=false!\n"));

/***/ })

});