{"version": 3, "sources": ["../../../src/build/output/log.ts"], "names": ["prefixes", "bootstrap", "wait", "error", "warn", "ready", "info", "event", "trace", "warnOnce", "white", "bold", "red", "yellow", "green", "magenta", "LOGGING_METHOD", "log", "prefixedLog", "prefixType", "message", "undefined", "length", "shift", "consoleMethod", "prefix", "console", "warnOnceMessages", "Set", "has", "add", "join"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;IAEaA,QAAQ;eAARA;;IAmCGC,SAAS;eAATA;;IAIAC,IAAI;eAAJA;;IAIAC,KAAK;eAALA;;IAIAC,IAAI;eAAJA;;IAIAC,KAAK;eAALA;;IAIAC,IAAI;eAAJA;;IAIAC,KAAK;eAALA;;IAIAC,KAAK;eAALA;;IAKAC,QAAQ;eAARA;;;4BAtEyC;AAElD,MAAMT,WAAW;IACtBE,MAAMQ,IAAAA,iBAAK,EAACC,IAAAA,gBAAI,EAAC;IACjBR,OAAOS,IAAAA,eAAG,EAACD,IAAAA,gBAAI,EAAC;IAChBP,MAAMS,IAAAA,kBAAM,EAACF,IAAAA,gBAAI,EAAC;IAClBN,OAAO;IACPC,MAAMI,IAAAA,iBAAK,EAACC,IAAAA,gBAAI,EAAC;IACjBJ,OAAOO,IAAAA,iBAAK,EAACH,IAAAA,gBAAI,EAAC;IAClBH,OAAOO,IAAAA,mBAAO,EAACJ,IAAAA,gBAAI,EAAC;AACtB;AAEA,MAAMK,iBAAiB;IACrBC,KAAK;IACLb,MAAM;IACND,OAAO;AACT;AAEA,SAASe,YAAYC,UAAiC,EAAE,GAAGC,OAAc;IACvE,IAAI,AAACA,CAAAA,OAAO,CAAC,EAAE,KAAK,MAAMA,OAAO,CAAC,EAAE,KAAKC,SAAQ,KAAMD,QAAQE,MAAM,KAAK,GAAG;QAC3EF,QAAQG,KAAK;IACf;IAEA,MAAMC,gBACJL,cAAcH,iBACVA,cAAc,CAACG,WAA0C,GACzD;IAEN,MAAMM,SAASzB,QAAQ,CAACmB,WAAW;IACnC,+DAA+D;IAC/D,IAAIC,QAAQE,MAAM,KAAK,GAAG;QACxBI,OAAO,CAACF,cAAc,CAAC;IACzB,OAAO;QACLE,OAAO,CAACF,cAAc,CAAC,MAAMC,WAAWL;IAC1C;AACF;AAEO,SAASnB,UAAU,GAAGmB,OAAc;IACzCM,QAAQT,GAAG,CAAC,QAAQG;AACtB;AAEO,SAASlB,KAAK,GAAGkB,OAAc;IACpCF,YAAY,WAAWE;AACzB;AAEO,SAASjB,MAAM,GAAGiB,OAAc;IACrCF,YAAY,YAAYE;AAC1B;AAEO,SAAShB,KAAK,GAAGgB,OAAc;IACpCF,YAAY,WAAWE;AACzB;AAEO,SAASf,MAAM,GAAGe,OAAc;IACrCF,YAAY,YAAYE;AAC1B;AAEO,SAASd,KAAK,GAAGc,OAAc;IACpCF,YAAY,WAAWE;AACzB;AAEO,SAASb,MAAM,GAAGa,OAAc;IACrCF,YAAY,YAAYE;AAC1B;AAEO,SAASZ,MAAM,GAAGY,OAAc;IACrCF,YAAY,YAAYE;AAC1B;AAEA,MAAMO,mBAAmB,IAAIC;AACtB,SAASnB,SAAS,GAAGW,OAAc;IACxC,IAAI,CAACO,iBAAiBE,GAAG,CAACT,OAAO,CAAC,EAAE,GAAG;QACrCO,iBAAiBG,GAAG,CAACV,QAAQW,IAAI,CAAC;QAElC3B,QAAQgB;IACV;AACF"}