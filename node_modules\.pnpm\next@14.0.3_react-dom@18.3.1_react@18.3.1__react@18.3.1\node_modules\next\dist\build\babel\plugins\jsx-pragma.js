"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
const _pluginsyntaxjsx = /*#__PURE__*/ _interop_require_default(require("next/dist/compiled/babel/plugin-syntax-jsx"));
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
function _default({ types: t }) {
    return {
        inherits: _pluginsyntaxjsx.default,
        visitor: {
            JSXElement (_path, state) {
                state.set("jsx", true);
            },
            // Fragment syntax is still JSX since it compiles to createElement(),
            // but JSXFragment is not a JSXElement
            JSXFragment (_path, state) {
                state.set("jsx", true);
            },
            Program: {
                exit (path, state) {
                    if (state.get("jsx")) {
                        const pragma = t.identifier(state.opts.pragma);
                        let importAs = pragma;
                        // if there's already a React in scope, use that instead of adding an import
                        const existingBinding = state.opts.reuseImport !== false && state.opts.importAs && path.scope.getBinding(state.opts.importAs);
                        // var _jsx = _pragma.createElement;
                        if (state.opts.property) {
                            if (state.opts.importAs) {
                                importAs = t.identifier(state.opts.importAs);
                            } else {
                                importAs = path.scope.generateUidIdentifier("pragma");
                            }
                            const mapping = t.variableDeclaration("var", [
                                t.variableDeclarator(pragma, t.memberExpression(importAs, t.identifier(state.opts.property)))
                            ]);
                            // if the React binding came from a require('react'),
                            // make sure that our usage comes after it.
                            let newPath;
                            if (existingBinding && t.isVariableDeclarator(existingBinding.path.node) && t.isCallExpression(existingBinding.path.node.init) && t.isIdentifier(existingBinding.path.node.init.callee) && existingBinding.path.node.init.callee.name === "require") {
                                [newPath] = existingBinding.path.parentPath.insertAfter(mapping);
                            } else {
                                [newPath] = path.unshiftContainer("body", mapping);
                            }
                            for (const declar of newPath.get("declarations")){
                                path.scope.registerBinding(newPath.node.kind, declar);
                            }
                        }
                        if (!existingBinding) {
                            const importSpecifier = t.importDeclaration([
                                state.opts.import ? t.importSpecifier(importAs, t.identifier(state.opts.import)) : state.opts.importNamespace ? t.importNamespaceSpecifier(importAs) : t.importDefaultSpecifier(importAs)
                            ], t.stringLiteral(state.opts.module || "react"));
                            const [newPath] = path.unshiftContainer("body", importSpecifier);
                            for (const specifier of newPath.get("specifiers")){
                                path.scope.registerBinding("module", specifier);
                            }
                        }
                    }
                }
            }
        }
    };
}

//# sourceMappingURL=jsx-pragma.js.map