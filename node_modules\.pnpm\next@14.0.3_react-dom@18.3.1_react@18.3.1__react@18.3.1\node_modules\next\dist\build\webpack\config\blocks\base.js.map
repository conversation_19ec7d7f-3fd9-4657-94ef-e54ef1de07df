{"version": 3, "sources": ["../../../../../src/build/webpack/config/blocks/base.ts"], "names": ["base", "curry", "ctx", "config", "mode", "isDevelopment", "name", "isServer", "isEdgeRuntime", "COMPILER_NAMES", "edgeServer", "server", "client", "target", "targetWeb", "process", "env", "__NEXT_TEST_MODE", "__NEXT_TEST_WITH_DEVTOOL", "devtool", "serverSourceMaps", "productionBrowserSourceMaps", "isClient", "module", "rules"], "mappings": ";;;;+BAKaA;;;eAAAA;;;oEALK;2BAEa;;;;;;AAGxB,MAAMA,OAAOC,IAAAA,oBAAK,EAAC,SAASD,KACjCE,GAAyB,EACzBC,MAA6B;IAE7BA,OAAOC,IAAI,GAAGF,IAAIG,aAAa,GAAG,gBAAgB;IAClDF,OAAOG,IAAI,GAAGJ,IAAIK,QAAQ,GACtBL,IAAIM,aAAa,GACfC,yBAAc,CAACC,UAAU,GACzBD,yBAAc,CAACE,MAAM,GACvBF,yBAAc,CAACG,MAAM;IAEzBT,OAAOU,MAAM,GAAG,CAACX,IAAIY,SAAS,GAC1B,YAAY,6DAA6D;OACzEZ,IAAIM,aAAa,GACjB;QAAC;QAAO;KAAM,GACd;QAAC;QAAO;KAAM;IAElB,4DAA4D;IAC5D,IAAIN,IAAIG,aAAa,EAAE;QACrB,IAAIU,QAAQC,GAAG,CAACC,gBAAgB,IAAI,CAACF,QAAQC,GAAG,CAACE,wBAAwB,EAAE;YACzEf,OAAOgB,OAAO,GAAG;QACnB,OAAO;YACL,+DAA+D;YAC/D,kEAAkE;YAClE,mEAAmE;YACnE,uDAAuD;YACvDhB,OAAOgB,OAAO,GAAG;QACnB;IACF,OAAO;QACL,IACEjB,IAAIM,aAAa,IAChBN,IAAIK,QAAQ,IAAIL,IAAIkB,gBAAgB,IACrC,6BAA6B;QAC5BlB,IAAImB,2BAA2B,IAAInB,IAAIoB,QAAQ,EAChD;YACAnB,OAAOgB,OAAO,GAAG;QACnB,OAAO;YACLhB,OAAOgB,OAAO,GAAG;QACnB;IACF;IAEA,IAAI,CAAChB,OAAOoB,MAAM,EAAE;QAClBpB,OAAOoB,MAAM,GAAG;YAAEC,OAAO,EAAE;QAAC;IAC9B;IAEA,6EAA6E;IAC7E,mDAAmD;IAEnD,OAAOrB;AACT"}