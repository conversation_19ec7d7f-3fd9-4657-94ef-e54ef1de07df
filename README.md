# 2second: Vintage Fashion E-commerce Platform

A modern e-commerce platform built with Next.js 13, focusing on vintage fashion retail. The application features both customer-facing shopping experiences and administrative capabilities.

## Features

### Customer Features- Browse vintage fashion products with filtering and sorting options
- View detailed product information with multiple images- Responsive product gallery
- Category-based navigation
- Pagination for product listings### Admin Features
- Secure admin dashboard- Product management (CRUD operations)
- Inventory tracking
- Order management
- Image handling with Cloudinary integration## Technical Stack
- **Frontend**: Next.js 13, React, Tailwind CSS- **Backend**: Next.js API Routes
- **Database**: MongoDB with Mongoose- **Authentication**: NextAuth.js
- **Image Storage**: Cloudinary- **Styling**: Tailwind CSS

## Getting Started
1. Clone the repository
2. Install dependencies:
   ```bash   npm install   ```
3. Set up environment variables:   ```
   MONGODB_URI=your_mongodb_connection_string   NEXTAUTH_SECRET=your_auth_secret
   CLOUDINARY_URL=your_cloudinary_url   ```
4. Run the development server:   ```bash
   npm run dev   ```

## Project Structure
- `/app` - Next.js 13 app directory containing routes and pages
- `/components` - Reusable React components- `/lib` - Utility functions and database connection
- `/models` - Mongoose models
- `/public` - Static assets## Contributing
1. Fork the repository
2. Create a feature branch3. Commit your changes
4. Push to the branch5. Create a Pull Request

## License

MIT License
































