{"version": 3, "sources": ["../../../../src/build/webpack/plugins/define-env-plugin.ts"], "names": ["getDefineEnv", "getDefineEnvPlugin", "errorIfEnvConflicted", "config", "key", "isPrivateKey", "test", "hasNextRuntimeKey", "Error", "configFileName", "isTurbopack", "allowedRevalidateHeaderKeys", "clientRouterFilters", "dev", "distDir", "fetchCacheKeyPrefix", "hasRewrites", "isClient", "isEdgeServer", "isNodeOrEdgeCompilation", "isNodeServer", "middlewareMatchers", "previewModeId", "__NEXT_DEFINE_ENV", "Object", "keys", "process", "env", "reduce", "prev", "startsWith", "JSON", "stringify", "acc", "EdgeRuntime", "NEXT_EDGE_RUNTIME_PROVIDER", "experimental", "windowHistorySupport", "useDeploymentIdServerActions", "deploymentId", "manualClientBasePath", "clientRouterFilter", "staticFilter", "dynamicFilter", "optimisticClientCache", "middlewarePrefetch", "crossOrigin", "__NEXT_TEST_MODE", "trailingSlash", "devIndicators", "buildActivity", "buildActivityPosition", "reactStrictMode", "optimizeFonts", "optimizeCss", "nextScriptWorkers", "scrollRestoration", "deviceSizes", "images", "imageSizes", "path", "loader", "dangerouslyAllowSVG", "unoptimized", "domains", "remotePatterns", "output", "basePath", "strictNextHead", "i18n", "analyticsId", "skipMiddlewareUrlNormalize", "externalMiddlewareRewritesResolve", "skipTrailingSlashRedirect", "webVitalsAttribution", "length", "assetPrefix", "undefined", "needsExperimentalReact", "options", "webpack", "DefinePlugin"], "mappings": ";;;;;;;;;;;;;;;IAwCgBA,YAAY;eAAZA;;IA8LAC,kBAAkB;eAAlBA;;;yBApOQ;wCACe;AAEvC,SAASC,qBAAqBC,MAA0B,EAAEC,GAAW;IACnE,MAAMC,eAAe,2BAA2BC,IAAI,CAACF;IACrD,MAAMG,oBAAoBH,QAAQ;IAElC,IAAIC,gBAAgBE,mBAAmB;QACrC,MAAM,IAAIC,MACR,CAAC,SAAS,EAAEJ,IAAI,iBAAiB,EAAED,OAAOM,cAAc,CAAC,qEAAqE,CAAC;IAEnI;AACF;AA0BO,SAAST,aAAa,EAC3BU,WAAW,EACXC,2BAA2B,EAC3BC,mBAAmB,EACnBT,MAAM,EACNU,GAAG,EACHC,OAAO,EACPC,mBAAmB,EACnBC,WAAW,EACXC,QAAQ,EACRC,YAAY,EACZC,uBAAuB,EACvBC,YAAY,EACZC,kBAAkB,EAClBC,aAAa,EACU;QAyHNnB,gBAKSA,iBAY0BA;IAzIpD,OAAO;QACL,+CAA+C;QAC/CoB,mBAAmB;QAEnB,GAAGC,OAAOC,IAAI,CAACC,QAAQC,GAAG,EAAEC,MAAM,CAChC,CAACC,MAAiCzB;YAChC,IAAIA,IAAI0B,UAAU,CAAC,iBAAiB;gBAClCD,IAAI,CAAC,CAAC,YAAY,EAAEzB,IAAI,CAAC,CAAC,GAAG2B,KAAKC,SAAS,CAACN,QAAQC,GAAG,CAACvB,IAAI;YAC9D;YACA,OAAOyB;QACT,GACA,CAAC,EACF;QACD,GAAGL,OAAOC,IAAI,CAACtB,OAAOwB,GAAG,EAAEC,MAAM,CAAC,CAACK,KAAK7B;YACtCF,qBAAqBC,QAAQC;YAE7B,OAAO;gBACL,GAAG6B,GAAG;gBACN,CAAC,CAAC,YAAY,EAAE7B,IAAI,CAAC,CAAC,EAAE2B,KAAKC,SAAS,CAAC7B,OAAOwB,GAAG,CAACvB,IAAI;YACxD;QACF,GAAG,CAAC,EAAE;QACN,GAAI,CAACc,eACD,CAAC,IACD;YACEgB,aAAaH,KAAKC,SAAS,CACzB;;;;aAIC,GACDN,QAAQC,GAAG,CAACQ,0BAA0B,IAAI;QAE9C,CAAC;QACL,qBAAqBJ,KAAKC,SAAS,CAACtB;QACpC,yBAAyBqB,KAAKC,SAAS,CAACtB;QACxC,6DAA6D;QAC7D,wBAAwBqB,KAAKC,SAAS,CAACnB,MAAM,gBAAgB;QAC7D,4BAA4BkB,KAAKC,SAAS,CACxCd,eAAe,SAASE,eAAe,WAAW;QAEpD,4BAA4BW,KAAKC,SAAS,CAAC;QAC3C,6CAA6CD,KAAKC,SAAS,CACzD7B,OAAOiC,YAAY,CAACC,oBAAoB;QAE1C,4CAA4CN,KAAKC,SAAS,CACxD7B,OAAOiC,YAAY,CAACE,4BAA4B;QAElD,kCAAkCP,KAAKC,SAAS,CAC9C7B,OAAOiC,YAAY,CAACG,YAAY,IAAI;QAEtC,6CACER,KAAKC,SAAS,CAACjB;QACjB,sCAAsCgB,KAAKC,SAAS,CAACV;QACrD,iDAAiDS,KAAKC,SAAS,CAC7DrB;QAEF,0CAA0CoB,KAAKC,SAAS,CACtDX,sBAAsB,EAAE;QAE1B,8CAA8CU,KAAKC,SAAS,CAC1D7B,OAAOiC,YAAY,CAACI,oBAAoB;QAE1C,mDAAmDT,KAAKC,SAAS,CAC/D7B,OAAOiC,YAAY,CAACK,kBAAkB;QAExC,6CAA6CV,KAAKC,SAAS,CACzDpB,uCAAAA,oBAAqB8B,YAAY;QAEnC,6CAA6CX,KAAKC,SAAS,CACzDpB,uCAAAA,oBAAqB+B,aAAa;QAEpC,8CAA8CZ,KAAKC,SAAS,CAC1D7B,OAAOiC,YAAY,CAACQ,qBAAqB;QAE3C,0CAA0Cb,KAAKC,SAAS,CACtD7B,OAAOiC,YAAY,CAACS,kBAAkB;QAExC,mCAAmCd,KAAKC,SAAS,CAAC7B,OAAO2C,WAAW;QACpE,mBAAmBf,KAAKC,SAAS,CAACf;QAClC,gCAAgCc,KAAKC,SAAS,CAC5CN,QAAQC,GAAG,CAACoB,gBAAgB;QAE9B,2FAA2F;QAC3F,GAAIlC,OAAQI,CAAAA,YAAYC,YAAW,IAC/B;YACE,+BAA+Ba,KAAKC,SAAS,CAAClB;QAChD,IACA,CAAC,CAAC;QACN,qCAAqCiB,KAAKC,SAAS,CAAC7B,OAAO6C,aAAa;QACxE,sCAAsCjB,KAAKC,SAAS,CAClD7B,OAAO8C,aAAa,CAACC,aAAa;QAEpC,+CAA+CnB,KAAKC,SAAS,CAC3D7B,OAAO8C,aAAa,CAACE,qBAAqB;QAE5C,kCAAkCpB,KAAKC,SAAS,CAC9C7B,OAAOiD,eAAe,KAAK,OAAO,QAAQjD,OAAOiD,eAAe;QAElE,sCAAsCrB,KAAKC,SAAS,CAClD,6EAA6E;QAC7E7B,OAAOiD,eAAe,KAAK,OAAO,OAAOjD,OAAOiD,eAAe;QAEjE,qCAAqCrB,KAAKC,SAAS,CACjD,CAACnB,OAAOV,OAAOkD,aAAa;QAE9B,mCAAmCtB,KAAKC,SAAS,CAC/C7B,OAAOiC,YAAY,CAACkB,WAAW,IAAI,CAACzC;QAEtC,qCAAqCkB,KAAKC,SAAS,CACjD7B,OAAOiC,YAAY,CAACmB,iBAAiB,IAAI,CAAC1C;QAE5C,yCAAyCkB,KAAKC,SAAS,CACrD7B,OAAOiC,YAAY,CAACoB,iBAAiB;QAEvC,iCAAiCzB,KAAKC,SAAS,CAAC;YAC9CyB,aAAatD,OAAOuD,MAAM,CAACD,WAAW;YACtCE,YAAYxD,OAAOuD,MAAM,CAACC,UAAU;YACpCC,MAAMzD,OAAOuD,MAAM,CAACE,IAAI;YACxBC,QAAQ1D,OAAOuD,MAAM,CAACG,MAAM;YAC5BC,qBAAqB3D,OAAOuD,MAAM,CAACI,mBAAmB;YACtDC,WAAW,EAAE5D,2BAAAA,iBAAAA,OAAQuD,MAAM,qBAAdvD,eAAgB4D,WAAW;YACxC,GAAIlD,MACA;gBACE,gEAAgE;gBAChEmD,SAAS7D,OAAOuD,MAAM,CAACM,OAAO;gBAC9BC,cAAc,GAAE9D,kBAAAA,OAAOuD,MAAM,qBAAbvD,gBAAe8D,cAAc;gBAC7CC,QAAQ/D,OAAO+D,MAAM;YACvB,IACA,CAAC,CAAC;QACR;QACA,sCAAsCnC,KAAKC,SAAS,CAAC7B,OAAOgE,QAAQ;QACpE,uCAAuCpC,KAAKC,SAAS,CACnD7B,OAAOiC,YAAY,CAACgC,cAAc;QAEpC,mCAAmCrC,KAAKC,SAAS,CAAChB;QAClD,oCAAoCe,KAAKC,SAAS,CAAC7B,OAAO+D,MAAM;QAChE,mCAAmCnC,KAAKC,SAAS,CAAC,CAAC,CAAC7B,OAAOkE,IAAI;QAC/D,mCAAmCtC,KAAKC,SAAS,EAAC7B,eAAAA,OAAOkE,IAAI,qBAAXlE,aAAa6D,OAAO;QACtE,mCAAmCjC,KAAKC,SAAS,CAAC7B,OAAOmE,WAAW;QACpE,kDAAkDvC,KAAKC,SAAS,CAC9D7B,OAAOoE,0BAA0B;QAEnC,0DAA0DxC,KAAKC,SAAS,CACtE7B,OAAOiC,YAAY,CAACoC,iCAAiC;QAEvD,4CAA4CzC,KAAKC,SAAS,CACxD7B,OAAOsE,yBAAyB;QAElC,iDAAiD1C,KAAKC,SAAS,CAC7D7B,OAAOiC,YAAY,CAACsC,oBAAoB,IACtCvE,OAAOiC,YAAY,CAACsC,oBAAoB,CAACC,MAAM,GAAG;QAEtD,6CAA6C5C,KAAKC,SAAS,CACzD7B,OAAOiC,YAAY,CAACsC,oBAAoB;QAE1C,mCAAmC3C,KAAKC,SAAS,CAAC7B,OAAOyE,WAAW;QACpE,GAAIzD,0BACA;YACE,+DAA+D;YAC/D,2DAA2D;YAC3D,+CAA+C;YAC/C,iBAAiBY,KAAKC,SAAS,CAAC;QAClC,IACA6C,SAAS;QACb,GAAI1D,0BACA;YACE,yCAAyCY,KAAKC,SAAS,CACrD8C,IAAAA,8CAAsB,EAAC3E;QAE3B,IACA0E,SAAS;IACf;AACF;AAEO,SAAS5E,mBAAmB8E,OAA+B;IAChE,OAAO,IAAIC,gBAAO,CAACC,YAAY,CAACjF,aAAa+E;AAC/C"}