{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/metadata/discover.ts"], "names": ["createStaticMetadataFromRoute", "createMetadataExportsCode", "METADATA_TYPE", "enumMetadataFiles", "dir", "filename", "extensions", "metadataResolver", "numericSuffix", "collectedFiles", "possibleFileNames", "concat", "Array", "fill", "map", "_", "index", "name", "resolved", "push", "resolvedDir", "segment", "isRootLayoutOrRootPage", "pageExtensions", "basePath", "hasStaticMetadataFiles", "staticImagesMetadata", "icon", "apple", "twitter", "openGraph", "manifest", "undefined", "collectIconModuleIfExists", "type", "staticManifestExtension", "manifestFile", "length", "ext", "path", "parse", "extension", "includes", "slice", "JSON", "stringify", "resolvedMetadataFiles", "STATIC_METADATA_IMAGES", "sort", "a", "b", "localeCompare", "for<PERSON>ach", "filepath", "imageModuleImportSource", "WEBPACK_RESOURCE_QUERIES", "metadata", "imageModule", "unshift", "join"], "mappings": ";;;;;;;;;;;;;;;IA6CsBA,6BAA6B;eAA7BA;;IA8FNC,yBAAyB;eAAzBA;;;6DAvIC;6BACS;iCACa;2BACE;;;;;;AAGzC,MAAMC,gBAAgB;AAEtB,mGAAmG;AACnG,eAAeC,kBACbC,GAAW,EACXC,QAAgB,EAChBC,UAA6B,EAC7B,EACEC,gBAAgB,EAChB,uFAAuF;AACvFC,aAAa,EAId;IAED,MAAMC,iBAA2B,EAAE;IAEnC,MAAMC,oBAAoB;QAACL;KAAS,CAACM,MAAM,CACzCH,gBACII,MAAM,IACHC,IAAI,CAAC,GACLC,GAAG,CAAC,CAACC,GAAGC,QAAUX,WAAWW,SAChC,EAAE;IAER,KAAK,MAAMC,QAAQP,kBAAmB;QACpC,MAAMQ,WAAW,MAAMX,iBAAiBH,KAAKa,MAAMX;QACnD,IAAIY,UAAU;YACZT,eAAeU,IAAI,CAACD;QACtB;IACF;IAEA,OAAOT;AACT;AAEO,eAAeT,8BACpBoB,WAAmB,EACnB,EACEC,OAAO,EACPd,gBAAgB,EAChBe,sBAAsB,EACtBC,cAAc,EACdC,QAAQ,EAOT;IAED,IAAIC,yBAAyB;IAC7B,MAAMC,uBAA2C;QAC/CC,MAAM,EAAE;QACRC,OAAO,EAAE;QACTC,SAAS,EAAE;QACXC,WAAW,EAAE;QACbC,UAAUC;IACZ;IAEA,eAAeC,0BACbC,IAA8C;QAE9C,IAAIA,SAAS,YAAY;YACvB,MAAMC,0BAA0B;gBAAC;gBAAe;aAAO;YACvD,MAAMC,eAAe,MAAMjC,kBACzBiB,aACA,YACAe,wBAAwBxB,MAAM,CAACY,iBAC/B;gBAAEhB;gBAAkBC,eAAe;YAAM;YAE3C,IAAI4B,aAAaC,MAAM,GAAG,GAAG;gBAC3BZ,yBAAyB;gBACzB,MAAM,EAAER,IAAI,EAAEqB,GAAG,EAAE,GAAGC,aAAI,CAACC,KAAK,CAACJ,YAAY,CAAC,EAAE;gBAChD,MAAMK,YAAYN,wBAAwBO,QAAQ,CAACJ,IAAIK,KAAK,CAAC,MACzDL,IAAIK,KAAK,CAAC,KACV;gBACJjB,qBAAqBK,QAAQ,GAAGa,KAAKC,SAAS,CAAC,CAAC,CAAC,EAAE5B,KAAK,CAAC,EAAEwB,UAAU,CAAC;YACxE;YACA;QACF;QAEA,MAAMK,wBAAwB,MAAM3C,kBAClCiB,aACA2B,uCAAsB,CAACb,KAAK,CAAC7B,QAAQ,EACrC;eACK0C,uCAAsB,CAACb,KAAK,CAAC5B,UAAU;eACtC4B,SAAS,YAAY,EAAE,GAAGX;SAC/B,EACD;YAAEhB;YAAkBC,eAAe;QAAK;QAE1CsC,sBACGE,IAAI,CAAC,CAACC,GAAGC,IAAMD,EAAEE,aAAa,CAACD,IAC/BE,OAAO,CAAC,CAACC;YACR,MAAMC,0BAA0B,CAAC,2BAA2B,EAAET,IAAAA,sBAAS,EACrE;gBACEX;gBACAb;gBACAG;gBACAD;YACF,GAEA,CAAC,EAAE8B,SAAS,CAAC,EAAEE,mCAAwB,CAACC,QAAQ,CAAC,CAAC;YAEpD,MAAMC,cAAc,CAAC,2DAA2D,EAAEb,KAAKC,SAAS,CAC9FS,yBACA,kBAAkB,CAAC;YACrB7B,yBAAyB;YACzB,IAAIS,SAAS,WAAW;gBACtBR,qBAAqBC,IAAI,CAAC+B,OAAO,CAACD;YACpC,OAAO;gBACL/B,oBAAoB,CAACQ,KAAK,CAACf,IAAI,CAACsC;YAClC;QACF;IACJ;IAEA,iEAAiE;IACjE,MAAMxB,0BAA0B;IAChC,MAAMA,0BAA0B;IAChC,MAAMA,0BAA0B;IAChC,MAAMA,0BAA0B;IAChC,IAAIX,wBAAwB;QAC1B,MAAMW,0BAA0B;QAChC,MAAMA,0BAA0B;IAClC;IAEA,OAAOR,yBAAyBC,uBAAuB;AACzD;AAEO,SAASzB,0BACduD,QAAmE;IAEnE,OAAOA,WACH,CAAC,EAAEtD,cAAc;WACZ,EAAEsD,SAAS7B,IAAI,CAACgC,IAAI,CAAC,KAAK;YACzB,EAAEH,SAAS5B,KAAK,CAAC+B,IAAI,CAAC,KAAK;gBACvB,EAAEH,SAAS1B,SAAS,CAAC6B,IAAI,CAAC,KAAK;cACjC,EAAEH,SAAS3B,OAAO,CAAC8B,IAAI,CAAC,KAAK;cAC7B,EAAEH,SAASzB,QAAQ,GAAGyB,SAASzB,QAAQ,GAAG,YAAY;GACjE,CAAC,GACE;AACN"}