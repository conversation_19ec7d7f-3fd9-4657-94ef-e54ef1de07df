{"version": 3, "sources": ["../../../../../../src/build/webpack/loaders/resolve-url-loader/lib/value-processor.ts"], "names": ["valueProcessor", "filename", "options", "URL_STATEMENT_REGEX", "directory", "path", "dirname", "join", "transformValue", "value", "candidate", "split", "map", "token", "i", "arr", "initialised", "mod", "before", "after", "isQuoted", "unescaped", "replace", "uri", "absolute", "testIsRelative", "testIsAbsolute", "query", "<PERSON><PERSON><PERSON><PERSON>", "slice", "loaderUtils", "urlToRequest", "relative", "isUrlRequest", "isAbsolute", "indexOf", "root", "test"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;AAsBA;;;;+BA+GA;;;eAAA;;;qEA7GwB;6DACP;;;;;;AAEjB,SAASA,eAAeC,QAAa,EAAEC,OAAY;IACjD,MAAMC,sBACJ;IACF,MAAMC,YAAYC,aAAI,CAACC,OAAO,CAACL;IAC/B,MAAMM,OAAOL,QAAQK,IAAI,CAACN,UAAUC;IAEpC;;;GAGC,GACD,OAAO,SAASM,eACd,sEAAsE,GACtEC,KAAa,EACb,yEAAyE,GACzEC,SAAc;QAEd,iDAAiD;QACjD,mDAAmD;QACnD,sEAAsE;QACtE,yCAAyC;QACzC,OAAOD,MACJE,KAAK,CAACR,qBACNS,GAAG,CAAC,CAACC,OAAYC,GAAQC;YACxB,mEAAmE;YACnE,MAAMC,cAAcH,SAAS;YAE7B,qEAAqE;YACrE,MAAMI,MAAMH,IAAI;YAChB,IAAIG,QAAQ,KAAKA,QAAQ,GAAG;gBAC1B,6CAA6C;gBAC7C,MAAMC,SAASH,GAAG,CAACD,IAAI,EAAE,EACvBK,QAAQJ,GAAG,CAACD,IAAI,EAAE,EAClBM,WAAWF,WAAWC,SAAUD,CAAAA,WAAW,OAAOA,WAAW,GAAE,GAC/DG,YAAYD,WACRJ,YAAYM,OAAO,CAAC,UAAU,QAC9BN;gBAEN,2EAA2E;gBAC3E,MAAML,QAAQU,UAAUV,KAAK,CAAC,YAC5BY,MAAMZ,KAAK,CAAC,EAAE,EACda,WAEE,AADA,mEAAmE;gBAClEC,eAAeF,QAAQhB,KAAKgB,KAAKb,cAClC,mEAAmE;gBAClEgB,eAAeH,QAAQhB,KAAKgB,MAC/BI,QAAQzB,QAAQ0B,SAAS,GAAGjB,MAAMkB,KAAK,CAAC,GAAGtB,IAAI,CAAC,MAAM;gBAExD,2FAA2F;gBAC3F,wCAAwC;gBACxC,IAAI,CAACiB,UAAU;oBACb,OAAOR;gBACT,OAAO,IAAId,QAAQsB,QAAQ,EAAE;oBAC3B,OAAOA,SAASF,OAAO,CAAC,OAAO,OAAOK;gBACxC,OAAO;oBACL,OAAOG,qBAAW,CAACC,YAAY,CAC7B1B,aAAI,CAAC2B,QAAQ,CAAC5B,WAAWoB,UAAUF,OAAO,CAAC,OAAO,OAAOK;gBAE7D;YACF,OAEK;gBACH,OAAOX;YACT;QACF,GACCT,IAAI,CAAC;IACV;IAEA;;;;;;;GAOC,GACD,SAASkB,eACP,6CAA6C,GAC7CF,GAAY;QAEZ,OACE,CAAC,CAACA,OACFO,qBAAW,CAACG,YAAY,CAACV,KAAK,UAC9B,CAAClB,aAAI,CAAC6B,UAAU,CAACX,QACjBA,IAAIY,OAAO,CAAC,SAAS;IAEzB;IAEA;;;;;GAKC,GACD,SAAST,eACP,6CAA6C,GAC7CH,GAAY;QAEZ,OACE,CAAC,CAACA,OACF,OAAOrB,QAAQkC,IAAI,KAAK,YACxBN,qBAAW,CAACG,YAAY,CAACV,KAAKrB,QAAQkC,IAAI,KACzC,CAAA,MAAMC,IAAI,CAACd,QAAQlB,aAAI,CAAC6B,UAAU,CAACX,IAAG;IAE3C;AACF;MAEA,WAAevB"}