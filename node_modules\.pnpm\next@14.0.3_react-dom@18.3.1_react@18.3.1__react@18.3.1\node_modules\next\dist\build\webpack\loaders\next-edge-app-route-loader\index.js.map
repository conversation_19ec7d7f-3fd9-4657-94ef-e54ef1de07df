{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-edge-app-route-loader/index.ts"], "names": ["EdgeAppRouteLoader", "page", "absolutePagePath", "preferredRegion", "appDirLoader", "appDirLoaderBase64", "middlewareConfig", "middlewareConfigBase64", "getOptions", "<PERSON><PERSON><PERSON>", "from", "toString", "JSON", "parse", "_module", "Error", "buildInfo", "getModuleBuildInfo", "nextEdgeSSR", "isServerComponent", "isAppDir", "route", "stringifiedPagePath", "stringifyRequest", "modulePath", "substring", "length", "WEBPACK_RESOURCE_QUERIES", "edgeSSREntry", "loadEntrypoint", "VAR_USERLAND"], "mappings": ";;;;+BA4DA;;;eAAA;;;oCA5DmC;kCACF;2BAGQ;gCAEV;AAW/B,MAAMA,qBACJ;IACE,MAAM,EACJC,IAAI,EACJC,gBAAgB,EAChBC,eAAe,EACfC,cAAcC,qBAAqB,EAAE,EACrCC,kBAAkBC,yBAAyB,EAAE,EAC9C,GAAG,IAAI,CAACC,UAAU;IAEnB,MAAMJ,eAAeK,OAAOC,IAAI,CAACL,oBAAoB,UAAUM,QAAQ;IACvE,MAAML,mBAAqCM,KAAKC,KAAK,CACnDJ,OAAOC,IAAI,CAACH,wBAAwB,UAAUI,QAAQ;IAGxD,kDAAkD;IAClD,IAAI,CAAC,IAAI,CAACG,OAAO,EAAE,MAAM,IAAIC,MAAM;IAEnC,MAAMC,YAAYC,IAAAA,sCAAkB,EAAC,IAAI,CAACH,OAAO;IAEjDE,UAAUE,WAAW,GAAG;QACtBC,mBAAmB;QACnBlB,MAAMA;QACNmB,UAAU;IACZ;IACAJ,UAAUK,KAAK,GAAG;QAChBpB;QACAC;QACAC;QACAG;IACF;IAEA,MAAMgB,sBAAsBC,IAAAA,kCAAgB,EAAC,IAAI,EAAErB;IACnD,MAAMsB,aAAa,CAAC,EAAEpB,aAAa,EAAEkB,oBAAoBG,SAAS,CAChE,GACAH,oBAAoBI,MAAM,GAAG,GAC7B,CAAC,EAAEC,mCAAwB,CAACC,YAAY,CAAC,CAAC;IAE5C,OAAO,MAAMC,IAAAA,8BAAc,EAAC,kBAAkB;QAC5CC,cAAcN;IAChB;AACF;MAEF,WAAexB"}