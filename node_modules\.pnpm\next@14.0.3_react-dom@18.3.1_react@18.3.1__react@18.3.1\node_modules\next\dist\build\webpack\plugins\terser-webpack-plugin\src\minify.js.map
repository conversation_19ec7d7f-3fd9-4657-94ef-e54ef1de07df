{"version": 3, "sources": ["../../../../../../src/build/webpack/plugins/terser-webpack-plugin/src/minify.ts"], "names": ["minify", "buildTerserOptions", "terserOptions", "mangle", "sourceMap", "undefined", "format", "beautify", "output", "options", "name", "input", "inputSourceMap", "opts", "asObject", "result", "terser"], "mappings": ";;;;+BAqBsBA;;;eAAAA;;;+DArBH;;;;;;AAEnB,SAASC,mBAAmBC,gBAAqB,CAAC,CAAC;IACjD,OAAO;QACL,GAAGA,aAAa;QAChBC,QACED,cAAcC,MAAM,IAAI,OACpB,OACA,OAAOD,cAAcC,MAAM,KAAK,YAChCD,cAAcC,MAAM,GACpB;YAAE,GAAGD,cAAcC,MAAM;QAAC;QAChC,kCAAkC;QAClC,wCAAwC;QACxCC,WAAWC;QACX,oCAAoC;QACpC,GAAIH,cAAcI,MAAM,GACpB;YAAEA,QAAQ;gBAAEC,UAAU;gBAAO,GAAGL,cAAcI,MAAM;YAAC;QAAE,IACvD;YAAEE,QAAQ;gBAAED,UAAU;gBAAO,GAAGL,cAAcM,MAAM;YAAC;QAAE,CAAC;IAC9D;AACF;AAEO,eAAeR,OAAOS,OAAY;IACvC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,cAAc,EAAEV,aAAa,EAAE,GAAGO;IACvD,sBAAsB;IACtB,MAAMI,OAAOZ,mBAAmBC;IAEhC,kCAAkC;IAClC,IAAIU,gBAAgB;QAClB,aAAa;QACbC,KAAKT,SAAS,GAAG;YAAEU,UAAU;QAAK;IACpC;IAEA,MAAMC,SAAS,MAAMC,eAAM,CAAChB,MAAM,CAAC;QAAE,CAACU,KAAK,EAAEC;IAAM,GAAGE;IACtD,OAAOE;AACT"}