{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-flight-client-entry-loader.ts"], "names": ["transformSource", "modules", "server", "getOptions", "isServer", "Array", "isArray", "requests", "code", "filter", "request", "regexCSS", "test", "map", "JSON", "stringify", "join", "buildInfo", "getModuleBuildInfo", "_module", "rsc", "type", "RSC_MODULE_TYPES", "client"], "mappings": ";;;;+BAaA;;;eAAwBA;;;2BAbS;oCACE;uBACV;AAWV,SAASA;IACtB,IAAI,EAAEC,OAAO,EAAEC,MAAM,EAAE,GACrB,IAAI,CAACC,UAAU;IACjB,MAAMC,WAAWF,WAAW;IAE5B,IAAI,CAACG,MAAMC,OAAO,CAACL,UAAU;QAC3BA,UAAUA,UAAU;YAACA;SAAQ,GAAG,EAAE;IACpC;IAEA,MAAMM,WAAWN;IACjB,MAAMO,OAAOD,QACX,8CAA8C;KAC7CE,MAAM,CAAC,CAACC,UAAaN,WAAW,CAACO,eAAQ,CAACC,IAAI,CAACF,WAAW,MAC1DG,GAAG,CACF,CAACH,UACC,CAAC,kCAAkC,EAAEI,KAAKC,SAAS,CAACL,SAAS,CAAC,CAAC,EAElEM,IAAI,CAAC;IAER,MAAMC,YAAYC,IAAAA,sCAAkB,EAAC,IAAI,CAACC,OAAO;IAEjDF,UAAUG,GAAG,GAAG;QACdC,MAAMC,2BAAgB,CAACC,MAAM;IAC/B;IAEA,OAAOf;AACT"}