import { verifyAdminSession } from '@/lib/auth';
import Link from 'next/link';
import { connectToDatabase } from '@/lib/mongodb';
import Product from '@/models/Product';
import User from '@/models/User';

interface DashboardCardProps {
  title: string;
  count: string | number;
  link: string;
  description: string;
}

export default async function AdminDashboard() {
  // This will automatically redirect if not authenticated or not admin
  const session = await verifyAdminSession();

  // Get dashboard statistics
  await connectToDatabase();
  const [productCount, userCount] = await Promise.all([
    Product.countDocuments(),
    User.countDocuments()
  ]);
  
  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-sepia-900">Admin Dashboard</h1>
        <p className="text-sepia-700 mt-2">Welcome back, {session.user.name}</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <DashboardCard
          title="Products"
          count={productCount}
          link="/admin/products"
          description="Manage your product catalog"
        />
        <DashboardCard
          title="Users"
          count={userCount}
          link="/admin/users"
          description="View and manage user accounts"
        />
        <DashboardCard
          title="Orders"
          count="Coming Soon"
          link="/admin/orders"
          description="View and manage customer orders"
        />
      </div>
    </div>
  );
}

function DashboardCard({ title, count, link, description }: DashboardCardProps) {
  return (
    <Link href={link}>
      <div className="vintage-card p-6 hover:shadow-md transition-shadow">
        <h2 className="text-xl font-semibold text-sepia-900">{title}</h2>
        <p className="text-3xl font-bold my-2 text-rust-700">{count}</p>
        <p className="text-sepia-600">{description}</p>
      </div>
    </Link>
  );
}