/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("mongoose");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?0257\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Ccomponents%5Clayout%5CHeader.tsx&modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Cnode_modules%5C.pnpm%5Cnext%4014.0.3_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Cnode_modules%5C.pnpm%5Cnext%4014.0.3_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Playfair_Display%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22display%22%3A%22swap%22%2C%22variable%22%3A%22--font-playfair%22%7D%5D%2C%22variableName%22%3A%22playfair%22%7D&modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Cnode_modules%5C.pnpm%5Cnext%4014.0.3_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Montserrat%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22display%22%3A%22swap%22%2C%22variable%22%3A%22--font-montserrat%22%7D%5D%2C%22variableName%22%3A%22montserrat%22%7D&modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Capp%5Cglobals.css&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Ccomponents%5Clayout%5CHeader.tsx&modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Cnode_modules%5C.pnpm%5Cnext%4014.0.3_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Cnode_modules%5C.pnpm%5Cnext%4014.0.3_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Playfair_Display%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22display%22%3A%22swap%22%2C%22variable%22%3A%22--font-playfair%22%7D%5D%2C%22variableName%22%3A%22playfair%22%7D&modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Cnode_modules%5C.pnpm%5Cnext%4014.0.3_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Montserrat%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22display%22%3A%22swap%22%2C%22variable%22%3A%22--font-montserrat%22%7D%5D%2C%22variableName%22%3A%22montserrat%22%7D&modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Capp%5Cglobals.css&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/layout/Header.tsx */ \"(ssr)/./components/layout/Header.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/link.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Ccomponents%5Clayout%5CHeader.tsx&modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Cnode_modules%5C.pnpm%5Cnext%4014.0.3_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Cnode_modules%5C.pnpm%5Cnext%4014.0.3_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Playfair_Display%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22display%22%3A%22swap%22%2C%22variable%22%3A%22--font-playfair%22%7D%5D%2C%22variableName%22%3A%22playfair%22%7D&modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Cnode_modules%5C.pnpm%5Cnext%4014.0.3_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Montserrat%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22display%22%3A%22swap%22%2C%22variable%22%3A%22--font-montserrat%22%7D%5D%2C%22variableName%22%3A%22montserrat%22%7D&modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Capp%5Cglobals.css&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Cnode_modules%5C.pnpm%5Cnext%4014.0.3_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Cnode_modules%5C.pnpm%5Cnext%4014.0.3_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Cnode_modules%5C.pnpm%5Cnext%4014.0.3_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Cnode_modules%5C.pnpm%5Cnext%4014.0.3_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Cnode_modules%5C.pnpm%5Cnext%4014.0.3_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Cnode_modules%5C.pnpm%5Cnext%4014.0.3_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Cnode_modules%5C.pnpm%5Cnext%4014.0.3_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Cnode_modules%5C.pnpm%5Cnext%4014.0.3_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Cnode_modules%5C.pnpm%5Cnext%4014.0.3_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Cnode_modules%5C.pnpm%5Cnext%4014.0.3_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Cnode_modules%5C.pnpm%5Cnext%4014.0.3_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Cnode_modules%5C.pnpm%5Cnext%4014.0.3_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Cnode_modules%5C.pnpm%5Cnext%4014.0.3_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Cnode_modules%5C.pnpm%5Cnext%4014.0.3_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Cnode_modules%5C.pnpm%5Cnext%4014.0.3_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Cnode_modules%5C.pnpm%5Cnext%4014.0.3_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Cnode_modules%5C.pnpm%5Cnext%4014.0.3_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Cnode_modules%5C.pnpm%5Cnext%4014.0.3_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Cnode_modules%5C.pnpm%5Cnext%4014.0.3_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cimage-component.js&modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Cnode_modules%5C.pnpm%5Cnext%4014.0.3_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Cnode_modules%5C.pnpm%5Cnext%4014.0.3_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cimage-component.js&modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Cnode_modules%5C.pnpm%5Cnext%4014.0.3_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/image-component.js */ \"(ssr)/./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/image-component.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/link.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNC4wLjNfcmVhY3QtZG9tQDE4LjMuMV9yZWFjdEAxOC4zLjFfX3JlYWN0QDE4LjMuMS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDdGVoZXMlNUNEb2N1bWVudHMlNUN3ZWItZGV2ZWxvcG1lbnQlNUNmdWxsc3RhY2slNUMyc2Vjb25kJTVDbm9kZV9tb2R1bGVzJTVDLnBucG0lNUNuZXh0JTQwMTQuMC4zX3JlYWN0LWRvbSU0MDE4LjMuMV9yZWFjdCU0MDE4LjMuMV9fcmVhY3QlNDAxOC4zLjElNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZGlzdCU1Q2NsaWVudCU1Q2ltYWdlLWNvbXBvbmVudC5qcyZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q3RlaGVzJTVDRG9jdW1lbnRzJTVDd2ViLWRldmVsb3BtZW50JTVDZnVsbHN0YWNrJTVDMnNlY29uZCU1Q25vZGVfbW9kdWxlcyU1Qy5wbnBtJTVDbmV4dCU0MDE0LjAuM19yZWFjdC1kb20lNDAxOC4zLjFfcmVhY3QlNDAxOC4zLjFfX3JlYWN0JTQwMTguMy4xJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNjbGllbnQlNUNsaW5rLmpzJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw0V0FBeU87QUFDek8iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8yc2Vjb25kLz8zMzFlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcdGVoZXNcXFxcRG9jdW1lbnRzXFxcXHdlYi1kZXZlbG9wbWVudFxcXFxmdWxsc3RhY2tcXFxcMnNlY29uZFxcXFxub2RlX21vZHVsZXNcXFxcLnBucG1cXFxcbmV4dEAxNC4wLjNfcmVhY3QtZG9tQDE4LjMuMV9yZWFjdEAxOC4zLjFfX3JlYWN0QDE4LjMuMVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxpbWFnZS1jb21wb25lbnQuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHRlaGVzXFxcXERvY3VtZW50c1xcXFx3ZWItZGV2ZWxvcG1lbnRcXFxcZnVsbHN0YWNrXFxcXDJzZWNvbmRcXFxcbm9kZV9tb2R1bGVzXFxcXC5wbnBtXFxcXG5leHRAMTQuMC4zX3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yZWFjdEAxOC4zLjFcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcbGluay5qc1wiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Cnode_modules%5C.pnpm%5Cnext%4014.0.3_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cimage-component.js&modules=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Cnode_modules%5C.pnpm%5Cnext%4014.0.3_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./components/layout/Header.tsx":
/*!**************************************!*\
  !*** ./components/layout/Header.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Menu_Search_ShoppingBag_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Search,ShoppingBag,User,X!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.292.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_Search_ShoppingBag_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Search,ShoppingBag,User,X!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.292.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_Search_ShoppingBag_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Search,ShoppingBag,User,X!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.292.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_Search_ShoppingBag_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Search,ShoppingBag,User,X!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.292.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_Search_ShoppingBag_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Search,ShoppingBag,User,X!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.292.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Header() {\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const isActive = (path)=>pathname === path;\n    const navLinks = [\n        {\n            name: \"Home\",\n            path: \"/\"\n        },\n        {\n            name: \"Shop\",\n            path: \"/shop\"\n        },\n        {\n            name: \"Collections\",\n            path: \"/collections\"\n        },\n        {\n            name: \"About\",\n            path: \"/about\"\n        },\n        {\n            name: \"Contact\",\n            path: \"/contact\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"backdrop-blur-md bg-white/70 border-b border-sepia-200 shadow-sm sticky top-0 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between py-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/\",\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"inline-block w-8 h-8 rounded-full bg-gradient-to-tr from-rust-600 to-sage-400 flex items-center justify-center text-white font-bold text-xl shadow-md\",\n                                    children: \"VT\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 28,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"hidden sm:inline text-2xl font-playfair font-bold text-sepia-900 tracking-tight\",\n                                    children: \"Vintage Treasures\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 29,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden md:flex gap-2\",\n                            children: navLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: link.path,\n                                    className: `px-4 py-2 rounded-full transition-colors font-medium text-lg ${isActive(link.path) ? \"bg-rust-600 text-white shadow\" : \"text-sepia-800 hover:bg-sage-200 hover:text-rust-700\"}`,\n                                    children: link.name\n                                }, link.path, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"text-sepia-800 hover:text-rust-700 p-2 rounded-full hover:bg-sage-100 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Search_ShoppingBag_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/account\",\n                                    className: \"text-sepia-800 hover:text-rust-700 p-2 rounded-full hover:bg-sage-100 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Search_ShoppingBag_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/cart\",\n                                    className: \"text-sepia-800 hover:text-rust-700 p-2 rounded-full hover:bg-sage-100 transition-colors relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Search_ShoppingBag_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            size: 20\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"absolute -top-1 -right-1 bg-rust-600 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center border-2 border-white\",\n                                            children: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"md:hidden ml-2 text-sepia-800 hover:text-rust-700 p-2 rounded-full hover:bg-sage-100 transition-colors\",\n                                    onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                    children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Search_ShoppingBag_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        size: 24\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 29\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Search_ShoppingBag_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        size: 24\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 47\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"md:hidden mt-2 pb-4 animate-fade-in\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"flex flex-col gap-2\",\n                        children: navLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: link.path,\n                                    className: `block px-4 py-2 rounded-full font-medium text-lg transition-colors ${isActive(link.path) ? \"bg-rust-600 text-white shadow\" : \"text-sepia-800 hover:bg-sage-200 hover:text-rust-700\"}`,\n                                    onClick: ()=>setIsMenuOpen(false),\n                                    children: link.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 19\n                                }, this)\n                            }, link.path, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 17\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Header.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Header.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/layout/Header.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"40ba6e769cd7\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8yc2Vjb25kLy4vYXBwL2dsb2JhbHMuY3NzPzYwMmIiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI0MGJhNmU3NjljZDdcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Playfair_Display_arguments_subsets_latin_display_swap_variable_font_playfair_variableName_playfair___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Playfair_Display\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-playfair\"}],\"variableName\":\"playfair\"} */ \"(rsc)/./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Playfair_Display\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\",\\\"variable\\\":\\\"--font-playfair\\\"}],\\\"variableName\\\":\\\"playfair\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Playfair_Display_arguments_subsets_latin_display_swap_variable_font_playfair_variableName_playfair___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Playfair_Display_arguments_subsets_latin_display_swap_variable_font_playfair_variableName_playfair___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Montserrat_arguments_subsets_latin_display_swap_variable_font_montserrat_variableName_montserrat___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Montserrat\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-montserrat\"}],\"variableName\":\"montserrat\"} */ \"(rsc)/./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Montserrat\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\",\\\"variable\\\":\\\"--font-montserrat\\\"}],\\\"variableName\\\":\\\"montserrat\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Montserrat_arguments_subsets_latin_display_swap_variable_font_montserrat_variableName_montserrat___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Montserrat_arguments_subsets_latin_display_swap_variable_font_montserrat_variableName_montserrat___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_layout_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/Header */ \"(rsc)/./components/layout/Header.tsx\");\n/* harmony import */ var _components_layout_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/Footer */ \"(rsc)/./components/layout/Footer.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"Vintage Treasures | Curated Fashion from the Past\",\n    description: \"Discover unique vintage fashion pieces curated from different eras. Sustainable, stylish, and one-of-a-kind.\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: `${(next_font_google_target_css_path_app_layout_tsx_import_Playfair_Display_arguments_subsets_latin_display_swap_variable_font_playfair_variableName_playfair___WEBPACK_IMPORTED_MODULE_4___default().variable)} ${(next_font_google_target_css_path_app_layout_tsx_import_Montserrat_arguments_subsets_latin_display_swap_variable_font_montserrat_variableName_montserrat___WEBPACK_IMPORTED_MODULE_5___default().variable)}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"flex flex-col min-h-screen bg-cream-50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\layout.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"flex-grow\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\layout.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\layout.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\layout.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\layout.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(rsc)/./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_mongodb__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/mongodb */ \"(rsc)/./lib/mongodb.ts\");\n/* harmony import */ var _models_Product__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/models/Product */ \"(rsc)/./models/Product.ts\");\n\n\n\n\n\nasync function HomePage() {\n    await (0,_lib_mongodb__WEBPACK_IMPORTED_MODULE_3__.connectToDatabase)();\n    // Get featured products\n    const featuredProducts = await _models_Product__WEBPACK_IMPORTED_MODULE_4__[\"default\"].find({\n        featured: true\n    }).limit(4);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative h-[80vh] bg-vintage\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                src: \"/images/hero-vintage.jpg\",\n                                alt: \"Vintage Fashion Collection\",\n                                fill: true,\n                                className: \"object-cover object-center opacity-90\",\n                                priority: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                                lineNumber: 17,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-r from-sepia-900/70 to-transparent\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                                lineNumber: 24,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative container mx-auto px-4 h-full flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-lg text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl md:text-5xl lg:text-6xl font-bold mb-4\",\n                                    children: \"Timeless Style Rediscovered\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                                    lineNumber: 29,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg md:text-xl mb-8\",\n                                    children: \"Curated vintage fashion pieces from the 1920s to the 1990s, each with its own unique story and character.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/shop\",\n                                            className: \"btn-primary text-center\",\n                                            children: \"Shop Collection\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                                            lineNumber: 36,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/about\",\n                                            className: \"btn-secondary text-center\",\n                                            children: \"Our Story\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                                            lineNumber: 39,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 bg-cream-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold text-center mb-12\",\n                            children: \"Shop by Era\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                            children: [\n                                \"1950s\",\n                                \"1960s\",\n                                \"1970s\",\n                                \"1980s\"\n                            ].map((era)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: `/shop?era=${era.toLowerCase()}`,\n                                    className: \"group relative h-64 overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            src: `/images/era-${era.toLowerCase()}.jpg`,\n                                            alt: `${era} Fashion`,\n                                            fill: true,\n                                            className: \"object-cover group-hover:scale-105 transition-transform duration-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-sepia-900/30 group-hover:bg-sepia-900/20 transition-colors\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-bold text-white bg-sepia-900/60 px-6 py-2\",\n                                                children: era\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                                                lineNumber: 67,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, era, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold text-center mb-4\",\n                            children: \"Featured Treasures\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-center text-sepia-800 mb-12 max-w-2xl mx-auto\",\n                            children: \"Our carefully selected pieces that represent the best of vintage fashion, quality, and style.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6\",\n                            children: featuredProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: `/shop/products/${product._id}`,\n                                    className: \"vintage-card group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative h-80 overflow-hidden\",\n                                            children: [\n                                                product.images && product.images[0] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    src: product.images[0],\n                                                    alt: product.name,\n                                                    fill: true,\n                                                    className: \"object-cover group-hover:scale-105 transition-transform duration-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full h-full bg-gray-200\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 21\n                                                }, this),\n                                                product.inventory <= 3 && product.inventory > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-2 right-2 bg-rust-600 text-white text-xs px-2 py-1\",\n                                                    children: [\n                                                        \"Only \",\n                                                        product.inventory,\n                                                        \" left\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-medium text-lg\",\n                                                    children: product.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-rust-800 font-semibold mt-1\",\n                                                    children: [\n                                                        \"$\",\n                                                        product.price.toFixed(2)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-2 text-sm text-sepia-700\",\n                                                    children: product.category\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, product._id.toString(), true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mt-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/shop\",\n                                className: \"btn-secondary\",\n                                children: \"View All Products\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 bg-vintage\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row items-center gap-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"md:w-1/2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative h-96 w-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        src: \"/images/about-vintage.jpg\",\n                                        alt: \"Our Vintage Collection Process\",\n                                        fill: true,\n                                        className: \"object-cover rounded-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"md:w-1/2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl font-bold mb-4\",\n                                        children: \"Our Vintage Journey\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"vintage-divider w-24 mb-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sepia-800 mb-4\",\n                                        children: \"Each piece in our collection has been carefully sourced from around the world, with a focus on quality, uniqueness, and historical significance.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sepia-800 mb-6\",\n                                        children: \"We believe in sustainable fashion through the celebration of vintage pieces that have stood the test of time. By giving these garments a second life, we're reducing waste and preserving fashion history.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/about\",\n                                        className: \"btn-primary inline-block\",\n                                        children: \"Read Our Story\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 bg-sage-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold mb-4\",\n                            children: \"Join Our Vintage Community\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sepia-800 mb-8 max-w-2xl mx-auto\",\n                            children: \"Subscribe to our newsletter for early access to new arrivals, styling tips, and exclusive vintage fashion insights.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            className: \"max-w-md mx-auto flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"email\",\n                                    placeholder: \"Your email address\",\n                                    className: \"vintage-input flex-grow\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    className: \"bg-rust-600 text-white px-6 py-2 hover:bg-rust-700\",\n                                    children: \"Subscribe\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\app\\\\page.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./components/layout/Footer.tsx":
/*!**************************************!*\
  !*** ./components/layout/Footer.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Twitter!=!lucide-react */ \"(rsc)/./node_modules/.pnpm/lucide-react@0.292.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/instagram.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Twitter!=!lucide-react */ \"(rsc)/./node_modules/.pnpm/lucide-react@0.292.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/facebook.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Twitter!=!lucide-react */ \"(rsc)/./node_modules/.pnpm/lucide-react@0.292.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/twitter.js\");\n\n\n\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-cream-500 border-t border-sepia-200 mt-16\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:col-span-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-playfair font-bold text-sepia-900 mb-4\",\n                                    children: \"Vintage Treasures\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 11,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sepia-800 mb-4\",\n                                    children: \"Curated vintage fashion from the 1920s to the 1990s. Each piece tells a story.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 14,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-sepia-800 hover:text-rust-700\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Instagram_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                size: 20\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 19,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 18,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-sepia-800 hover:text-rust-700\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Instagram_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                size: 20\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 22,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 21,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-sepia-800 hover:text-rust-700\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Instagram_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                size: 20\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 25,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 24,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 17,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 10,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-sepia-900 mb-4\",\n                                    children: \"Shop\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/shop?category=dresses\",\n                                                className: \"text-sepia-800 hover:text-rust-700\",\n                                                children: \"Dresses\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 34,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 34,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/shop?category=tops\",\n                                                className: \"text-sepia-800 hover:text-rust-700\",\n                                                children: \"Tops\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 35,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 35,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/shop?category=bottoms\",\n                                                className: \"text-sepia-800 hover:text-rust-700\",\n                                                children: \"Bottoms\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 36,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 36,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/shop?category=outerwear\",\n                                                className: \"text-sepia-800 hover:text-rust-700\",\n                                                children: \"Outerwear\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 37,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 37,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/shop?category=accessories\",\n                                                className: \"text-sepia-800 hover:text-rust-700\",\n                                                children: \"Accessories\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 38,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 38,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-sepia-900 mb-4\",\n                                    children: \"Information\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/about\",\n                                                className: \"text-sepia-800 hover:text-rust-700\",\n                                                children: \"About Us\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 46,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/shipping\",\n                                                className: \"text-sepia-800 hover:text-rust-700\",\n                                                children: \"Shipping & Returns\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 47,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/care\",\n                                                className: \"text-sepia-800 hover:text-rust-700\",\n                                                children: \"Garment Care\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 48,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 48,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/size-guide\",\n                                                className: \"text-sepia-800 hover:text-rust-700\",\n                                                children: \"Size Guide\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 49,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/contact\",\n                                                className: \"text-sepia-800 hover:text-rust-700\",\n                                                children: \"Contact Us\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 50,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-sepia-900 mb-4\",\n                                    children: \"Join Our Newsletter\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sepia-800 mb-4\",\n                                    children: \"Subscribe to receive updates on new arrivals and special promotions.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    className: \"flex\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            placeholder: \"Your email\",\n                                            className: \"vintage-input flex-grow\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            className: \"bg-rust-600 text-white px-4 py-2 hover:bg-rust-700\",\n                                            children: \"Subscribe\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"vintage-divider my-8\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center text-sepia-700 text-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                \"\\xa9 \",\n                                new Date().getFullYear(),\n                                \" Vintage Treasures. All rights reserved.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/privacy\",\n                                    className: \"hover:text-rust-700\",\n                                    children: \"Privacy Policy\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this),\n                                \" • \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/terms\",\n                                    className: \"hover:text-rust-700\",\n                                    children: \"Terms of Service\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\web-development\\\\fullstack\\\\2second\\\\components\\\\layout\\\\Footer.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/layout/Footer.tsx\n");

/***/ }),

/***/ "(rsc)/./components/layout/Header.tsx":
/*!**************************************!*\
  !*** ./components/layout/Header.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\web-development\fullstack\2second\components\layout\Header.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./lib/mongodb.ts":
/*!************************!*\
  !*** ./lib/mongodb.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   connectToDatabase: () => (/* binding */ connectToDatabase)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst MONGODB_URI = process.env.MONGODB_URI;\nif (!MONGODB_URI) {\n    throw new Error(\"Please define the MONGODB_URI environment variable\");\n}\nlet cached = global.mongoose;\nif (!cached) {\n    cached = global.mongoose = {\n        conn: null,\n        promise: null\n    };\n}\nasync function connectToDatabase() {\n    if (cached.conn) {\n        return cached.conn;\n    }\n    if (!cached.promise) {\n        cached.promise = mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(MONGODB_URI);\n    }\n    cached.conn = await cached.promise;\n    return cached.conn;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvbW9uZ29kYi50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBZ0M7QUFFaEMsTUFBTUMsY0FBY0MsUUFBUUMsR0FBRyxDQUFDRixXQUFXO0FBRTNDLElBQUksQ0FBQ0EsYUFBYTtJQUNoQixNQUFNLElBQUlHLE1BQU07QUFDbEI7QUFFQSxJQUFJQyxTQUFTQyxPQUFPTixRQUFRO0FBRTVCLElBQUksQ0FBQ0ssUUFBUTtJQUNYQSxTQUFTQyxPQUFPTixRQUFRLEdBQUc7UUFBRU8sTUFBTTtRQUFNQyxTQUFTO0lBQUs7QUFDekQ7QUFFTyxlQUFlQztJQUNwQixJQUFJSixPQUFPRSxJQUFJLEVBQUU7UUFDZixPQUFPRixPQUFPRSxJQUFJO0lBQ3BCO0lBRUEsSUFBSSxDQUFDRixPQUFPRyxPQUFPLEVBQUU7UUFDbkJILE9BQU9HLE9BQU8sR0FBR1IsdURBQWdCLENBQUNDO0lBQ3BDO0lBRUFJLE9BQU9FLElBQUksR0FBRyxNQUFNRixPQUFPRyxPQUFPO0lBQ2xDLE9BQU9ILE9BQU9FLElBQUk7QUFDcEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8yc2Vjb25kLy4vbGliL21vbmdvZGIudHM/MDViZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgbW9uZ29vc2UgZnJvbSAnbW9uZ29vc2UnO1xuXG5jb25zdCBNT05HT0RCX1VSSSA9IHByb2Nlc3MuZW52Lk1PTkdPREJfVVJJITtcblxuaWYgKCFNT05HT0RCX1VSSSkge1xuICB0aHJvdyBuZXcgRXJyb3IoJ1BsZWFzZSBkZWZpbmUgdGhlIE1PTkdPREJfVVJJIGVudmlyb25tZW50IHZhcmlhYmxlJyk7XG59XG5cbmxldCBjYWNoZWQgPSBnbG9iYWwubW9uZ29vc2U7XG5cbmlmICghY2FjaGVkKSB7XG4gIGNhY2hlZCA9IGdsb2JhbC5tb25nb29zZSA9IHsgY29ubjogbnVsbCwgcHJvbWlzZTogbnVsbCB9O1xufVxuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gY29ubmVjdFRvRGF0YWJhc2UoKSB7XG4gIGlmIChjYWNoZWQuY29ubikge1xuICAgIHJldHVybiBjYWNoZWQuY29ubjtcbiAgfVxuXG4gIGlmICghY2FjaGVkLnByb21pc2UpIHtcbiAgICBjYWNoZWQucHJvbWlzZSA9IG1vbmdvb3NlLmNvbm5lY3QoTU9OR09EQl9VUkkpO1xuICB9XG4gIFxuICBjYWNoZWQuY29ubiA9IGF3YWl0IGNhY2hlZC5wcm9taXNlO1xuICByZXR1cm4gY2FjaGVkLmNvbm47XG59Il0sIm5hbWVzIjpbIm1vbmdvb3NlIiwiTU9OR09EQl9VUkkiLCJwcm9jZXNzIiwiZW52IiwiRXJyb3IiLCJjYWNoZWQiLCJnbG9iYWwiLCJjb25uIiwicHJvbWlzZSIsImNvbm5lY3RUb0RhdGFiYXNlIiwiY29ubmVjdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/mongodb.ts\n");

/***/ }),

/***/ "(rsc)/./models/Product.ts":
/*!***************************!*\
  !*** ./models/Product.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ProductSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    name: {\n        type: String,\n        required: true\n    },\n    description: {\n        type: String,\n        required: true\n    },\n    price: {\n        type: Number,\n        required: true\n    },\n    images: [\n        String\n    ],\n    category: {\n        type: String,\n        required: true\n    },\n    tags: [\n        String\n    ],\n    inventory: {\n        type: Number,\n        default: 0\n    },\n    featured: {\n        type: Boolean,\n        default: false\n    },\n    era: {\n        type: String\n    },\n    condition: {\n        type: String\n    },\n    size: {\n        type: String\n    },\n    material: {\n        type: String\n    },\n    createdAt: {\n        type: Date,\n        default: Date.now\n    },\n    updatedAt: {\n        type: Date,\n        default: Date.now\n    }\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Product || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Product\", ProductSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./models/Product.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1","vendor-chunks/lucide-react@0.292.0_react@18.3.1","vendor-chunks/@swc+helpers@0.5.2"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@14.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Ctehes%5CDocuments%5Cweb-development%5Cfullstack%5C2second&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();