{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/wellknown-errors-plugin/parseNextFontError.ts"], "names": ["getNextFontError", "err", "module", "resourceResolveData", "loaders", "find", "loader", "test", "file", "JSON", "parse", "query", "slice", "path", "name", "SimpleWebpackError", "message", "stack"], "mappings": ";;;;+BAEgBA;;;eAAAA;;;oCAFmB;AAE5B,SAASA,iBACdC,GAAU,EACVC,MAAW;IAEX,IAAI;QACF,MAAMC,sBAAsBD,OAAOC,mBAAmB;QACtD,IACE,CAACD,OAAOE,OAAO,CAACC,IAAI,CAAC,CAACC,SACpB,gCAAgCC,IAAI,CAACD,OAAOA,MAAM,IAEpD;YACA,OAAO;QACT;QAEA,mFAAmF;QACnF,2CAA2C;QAC3C,MAAME,OAAOC,KAAKC,KAAK,CAACP,oBAAoBQ,KAAK,CAACC,KAAK,CAAC,IAAIC,IAAI;QAEhE,IAAIZ,IAAIa,IAAI,KAAK,iBAAiB;YAChC,8DAA8D;YAC9D,OAAO,IAAIC,sCAAkB,CAC3BP,MACA,CAAC,sBAAsB,EAAEP,IAAIe,OAAO,CAAC,CAAC;QAE1C,OAAO;YACL,qCAAqC;YACrC,OAAO,IAAID,sCAAkB,CAC3BP,MACA,CAAC,sCAAsC,EAAEP,IAAIgB,KAAK,CAAC,CAAC;QAExD;IACF,EAAE,OAAM;QACN,OAAO;IACT;AACF"}