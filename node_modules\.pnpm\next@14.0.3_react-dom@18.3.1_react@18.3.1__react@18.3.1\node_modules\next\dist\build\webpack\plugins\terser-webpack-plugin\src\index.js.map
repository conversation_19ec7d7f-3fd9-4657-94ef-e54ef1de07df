{"version": 3, "sources": ["../../../../../../src/build/webpack/plugins/terser-webpack-plugin/src/index.ts"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getEcmaVersion", "environment", "arrowFunction", "const", "destructuring", "forOf", "module", "bigIntLiteral", "dynamicImport", "buildError", "error", "file", "line", "Error", "message", "col", "stack", "split", "slice", "join", "debugMinify", "process", "env", "NEXT_DEBUG_MINIFY", "constructor", "options", "terserOptions", "parallel", "swcMinify", "optimize", "compiler", "compilation", "assets", "optimizeOptions", "cache", "SourceMapSource", "RawSource", "compilationSpan", "spans", "get", "terserSpan", "<PERSON><PERSON><PERSON><PERSON>", "setAttribute", "name", "traceAsyncFn", "numberOfAssetsForMinify", "assetsList", "Object", "keys", "assetsForMinify", "Promise", "all", "filter", "ModuleFilenameHelpers", "matchObject", "bind", "undefined", "test", "res", "getAsset", "console", "log", "match", "info", "minimized", "map", "source", "eTag", "getLazyHashedEtag", "output", "getPromise", "JSON", "stringify", "toString", "<PERSON><PERSON><PERSON><PERSON>", "Infinity", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputSource", "numberOfWorkers", "Math", "min", "availableNumberOfCores", "initializedWorker", "getWorker", "minify", "result", "require", "input", "inputSourceMap", "sourceMap", "content", "compress", "mangle", "Worker", "path", "__dirname", "numWorkers", "enableWorkerThreads", "getStdout", "pipe", "stdout", "getStderr", "stderr", "limit", "pLimit", "scheduledTasks", "asset", "push", "minifySpan", "sourceFromInputSource", "sourceAndMap", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "javascriptModule", "errors", "code", "storePromise", "newInfo", "updateAsset", "end", "apply", "webpack", "sources", "ecma", "pluginName", "hooks", "thisCompilation", "tap", "getCache", "handleHashForChunk", "hash", "_chunk", "update", "JSModulesHooks", "javascript", "JavascriptModulesPlugin", "getCompilationHooks", "chunkHash", "chunk", "hasRuntime", "processAssets", "tapPromise", "stage", "Compilation", "PROCESS_ASSETS_STAGE_OPTIMIZE_SIZE", "statsPrinter", "stats", "print", "for", "green", "formatFlag"], "mappings": ";;;;+BAkDaA;;;eAAAA;;;8DAlDS;yBAKf;+DACY;4BACI;iCACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEtB,SAASC,eAAeC,WAAgB;IACtC,SAAS;IACT,IACEA,YAAYC,aAAa,IACzBD,YAAYE,KAAK,IACjBF,YAAYG,aAAa,IACzBH,YAAYI,KAAK,IACjBJ,YAAYK,MAAM,EAClB;QACA,OAAO;IACT;IAEA,UAAU;IACV,IAAIL,YAAYM,aAAa,IAAIN,YAAYO,aAAa,EAAE;QAC1D,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAASC,WAAWC,KAAU,EAAEC,IAAY;IAC1C,IAAID,MAAME,IAAI,EAAE;QACd,OAAO,IAAIC,MACT,CAAC,EAAEF,KAAK,cAAc,EAAED,MAAMI,OAAO,CAAC,EAAE,EAAEH,KAAK,CAAC,EAAED,MAAME,IAAI,CAAC,CAAC,EAC5DF,MAAMK,GAAG,CACV,CAAC,EACAL,MAAMM,KAAK,GAAG,CAAC,EAAE,EAAEN,MAAMM,KAAK,CAACC,KAAK,CAAC,MAAMC,KAAK,CAAC,GAAGC,IAAI,CAAC,MAAM,CAAC,GAAG,GACpE,CAAC;IAEN;IAEA,IAAIT,MAAMM,KAAK,EAAE;QACf,OAAO,IAAIH,MAAM,CAAC,EAAEF,KAAK,cAAc,EAAED,MAAMI,OAAO,CAAC,EAAE,EAAEJ,MAAMM,KAAK,CAAC,CAAC;IAC1E;IAEA,OAAO,IAAIH,MAAM,CAAC,EAAEF,KAAK,cAAc,EAAED,MAAMI,OAAO,CAAC,CAAC;AAC1D;AAEA,MAAMM,cAAcC,QAAQC,GAAG,CAACC,iBAAiB;AAE1C,MAAMxB;IAEXyB,YAAYC,UAAe,CAAC,CAAC,CAAE;QAC7B,MAAM,EAAEC,gBAAgB,CAAC,CAAC,EAAEC,QAAQ,EAAEC,SAAS,EAAE,GAAGH;QAEpD,IAAI,CAACA,OAAO,GAAG;YACbG;YACAD;YACAD;QACF;IACF;IAEA,MAAMG,SACJC,QAAa,EACbC,WAAgB,EAChBC,MAAW,EACXC,eAAoB,EACpBC,KAAU,EACV,EAAEC,eAAe,EAAEC,SAAS,EAAO,EACnC;QACA,MAAMC,kBAAkBC,sBAAK,CAACC,GAAG,CAACR,gBAAiBO,sBAAK,CAACC,GAAG,CAACT;QAC7D,MAAMU,aAAaH,gBAAgBI,UAAU,CAC3C;QAEFD,WAAWE,YAAY,CAAC,mBAAmBX,YAAYY,IAAI;QAC3DH,WAAWE,YAAY,CAAC,aAAa,IAAI,CAACjB,OAAO,CAACG,SAAS;QAE3D,OAAOY,WAAWI,YAAY,CAAC;YAC7B,IAAIC,0BAA0B;YAC9B,MAAMC,aAAaC,OAAOC,IAAI,CAAChB;YAE/B,MAAMiB,kBAAkB,MAAMC,QAAQC,GAAG,CACvCL,WACGM,MAAM,CAAC,CAACT;gBACP,IACE,CAACU,8BAAqB,CAACC,WAAW,CAACC,IAAI,CACrC,wCAAwC;gBACxCC,WACA;oBAAEC,MAAM;gBAAqB,GAC7Bd,OACF;oBACA,OAAO;gBACT;gBAEA,MAAMe,MAAM3B,YAAY4B,QAAQ,CAAChB;gBACjC,IAAI,CAACe,KAAK;oBACRE,QAAQC,GAAG,CAAClB;oBACZ,OAAO;gBACT;gBAEA,yDAAyD;gBACzD,gEAAgE;gBAChE,IACEA,KAAKmB,KAAK,CACR,2DAEF;oBACA,OAAO;gBACT;gBAEA,MAAM,EAAEC,IAAI,EAAE,GAAGL;gBAEjB,qDAAqD;gBACrD,IAAIK,KAAKC,SAAS,EAAE;oBAClB,OAAO;gBACT;gBAEA,OAAO;YACT,GACCC,GAAG,CAAC,OAAOtB;gBACV,MAAM,EAAEoB,IAAI,EAAEG,MAAM,EAAE,GAAGnC,YAAY4B,QAAQ,CAAChB;gBAE9C,MAAMwB,OAAOjC,MAAMkC,iBAAiB,CAACF;gBACrC,MAAMG,SAAS,MAAMnC,MAAMoC,UAAU,CAAC3B,MAAMwB;gBAE5C,IAAI,CAACE,QAAQ;oBACXxB,2BAA2B;gBAC7B;gBAEA,IAAIzB,eAAeA,gBAAgB,KAAK;oBACtCwC,QAAQC,GAAG,CACTU,KAAKC,SAAS,CAAC;wBACb7B;wBACAuB,QAAQA,OAAOA,MAAM,GAAGO,QAAQ;oBAClC,IACA;wBACEC,aAAaC;wBACbC,iBAAiBD;oBACnB;gBAEJ;gBACA,OAAO;oBAAEhC;oBAAMoB;oBAAMc,aAAaX;oBAAQG;oBAAQF;gBAAK;YACzD;YAGJ,MAAMW,kBAAkBC,KAAKC,GAAG,CAC9BnC,yBACAZ,gBAAgBgD,sBAAsB;YAGxC,IAAIC;YAEJ,6CAA6C;YAC7C,MAAMC,YAAY;gBAChB,IAAI,IAAI,CAAC1D,OAAO,CAACG,SAAS,EAAE;oBAC1B,OAAO;wBACLwD,QAAQ,OAAO3D;4BACb,MAAM4D,SAAS,MAAMC,QAAQ,mBAAmBF,MAAM,CACpD3D,QAAQ8D,KAAK,EACb;gCACE,GAAI9D,QAAQ+D,cAAc,GACtB;oCACEC,WAAW;wCACTC,SAASnB,KAAKC,SAAS,CAAC/C,QAAQ+D,cAAc;oCAChD;gCACF,IACA,CAAC,CAAC;gCACNG,UAAU;gCACVC,QAAQ;4BACV;4BAGF,OAAOP;wBACT;oBACF;gBACF;gBAEA,IAAIH,mBAAmB;oBACrB,OAAOA;gBACT;gBAEAA,oBAAoB,IAAIW,kBAAM,CAACC,MAAK3E,IAAI,CAAC4E,WAAW,gBAAgB;oBAClEC,YAAYlB;oBACZmB,qBAAqB;gBACvB;gBAEAf,kBAAkBgB,SAAS,GAAGC,IAAI,CAAC9E,QAAQ+E,MAAM;gBACjDlB,kBAAkBmB,SAAS,GAAGF,IAAI,CAAC9E,QAAQiF,MAAM;gBAEjD,OAAOpB;YACT;YAEA,MAAMqB,QAAQC,IAAAA,eAAM,EAClB,mEAAmE;YACnE,IAAI,CAAC/E,OAAO,CAACG,SAAS,GAClB+C,WACA9B,0BAA0B,IAC1BiC,kBACAH;YAEN,MAAM8B,iBAAiB,EAAE;YAEzB,KAAK,MAAMC,SAASzD,gBAAiB;gBACnCwD,eAAeE,IAAI,CACjBJ,MAAM;oBACJ,MAAM,EAAE5D,IAAI,EAAEkC,WAAW,EAAEd,IAAI,EAAEI,IAAI,EAAE,GAAGuC;oBAC1C,IAAI,EAAErC,MAAM,EAAE,GAAGqC;oBAEjB,MAAME,aAAapE,WAAWC,UAAU,CAAC;oBACzCmE,WAAWlE,YAAY,CAAC,QAAQC;oBAChCiE,WAAWlE,YAAY,CACrB,SACA,OAAO2B,WAAW,cAAc,SAAS;oBAG3C,OAAOuC,WAAWhE,YAAY,CAAC;wBAC7B,IAAI,CAACyB,QAAQ;4BACX,MAAM,EAAEH,QAAQ2C,qBAAqB,EAAE5C,KAAKuB,cAAc,EAAE,GAC1DX,YAAYiC,YAAY;4BAE1B,MAAMvB,QAAQwB,OAAOC,QAAQ,CAACH,yBAC1BA,sBAAsBpC,QAAQ,KAC9BoC;4BAEJ,MAAMpF,UAAU;gCACdkB;gCACA4C;gCACAC;gCACA9D,eAAe;oCAAE,GAAG,IAAI,CAACD,OAAO,CAACC,aAAa;gCAAC;4BACjD;4BAEA,IAAI,OAAOD,QAAQC,aAAa,CAACpB,MAAM,KAAK,aAAa;gCACvD,IAAI,OAAOyD,KAAKkD,gBAAgB,KAAK,aAAa;oCAChDxF,QAAQC,aAAa,CAACpB,MAAM,GAAGyD,KAAKkD,gBAAgB;gCACtD,OAAO,IAAI,iBAAiBxD,IAAI,CAACd,OAAO;oCACtClB,QAAQC,aAAa,CAACpB,MAAM,GAAG;gCACjC,OAAO,IAAI,iBAAiBmD,IAAI,CAACd,OAAO;oCACtClB,QAAQC,aAAa,CAACpB,MAAM,GAAG;gCACjC;4BACF;4BAEA,IAAI;gCACF+D,SAAS,MAAMc,YAAYC,MAAM,CAAC3D;4BACpC,EAAE,OAAOf,OAAO;gCACdqB,YAAYmF,MAAM,CAACP,IAAI,CAAClG,WAAWC,OAAOiC;gCAE1C;4BACF;4BAEA,IAAI0B,OAAOJ,GAAG,EAAE;gCACdI,OAAOH,MAAM,GAAG,IAAI/B,gBAClBkC,OAAO8C,IAAI,EACXxE,MACA0B,OAAOJ,GAAG,EACVsB,OACAC,gBACA;4BAEJ,OAAO;gCACLnB,OAAOH,MAAM,GAAG,IAAI9B,UAAUiC,OAAO8C,IAAI;4BAC3C;4BAEA,MAAMjF,MAAMkF,YAAY,CAACzE,MAAMwB,MAAM;gCACnCD,QAAQG,OAAOH,MAAM;4BACvB;wBACF;wBAEA,MAAMmD,UAAU;4BAAErD,WAAW;wBAAK;wBAClC,MAAM,EAAEE,MAAM,EAAE,GAAGG;wBAEnBtC,YAAYuF,WAAW,CAAC3E,MAAMuB,QAAQmD;oBACxC;gBACF;YAEJ;YAEA,MAAMnE,QAAQC,GAAG,CAACsD;YAElB,IAAIvB,mBAAmB;gBACrB,MAAMA,kBAAkBqC,GAAG;YAC7B;QACF;IACF;IAEAC,MAAM1F,QAAa,EAAE;YACoBA;QAAvC,MAAM,EAAEK,eAAe,EAAEC,SAAS,EAAE,GAAGN,CAAAA,6BAAAA,oBAAAA,SAAU2F,OAAO,qBAAjB3F,kBAAmB4F,OAAO,KAAIA,gBAAO;QAC5E,MAAM,EAAErD,MAAM,EAAE,GAAGvC,SAASL,OAAO;QAEnC,IAAI,OAAO,IAAI,CAACA,OAAO,CAACC,aAAa,CAACiG,IAAI,KAAK,aAAa;YAC1D,IAAI,CAAClG,OAAO,CAACC,aAAa,CAACiG,IAAI,GAAG3H,eAAeqE,OAAOpE,WAAW,IAAI,CAAC;QAC1E;QAEA,MAAM2H,aAAa,IAAI,CAACpG,WAAW,CAACmB,IAAI;QACxC,MAAMsC,yBAAyB,IAAI,CAACxD,OAAO,CAACE,QAAQ;QAEpDG,SAAS+F,KAAK,CAACC,eAAe,CAACC,GAAG,CAACH,YAAY,CAAC7F;YAC9C,MAAMG,QAAQH,YAAYiG,QAAQ,CAAC;YAEnC,MAAMC,qBAAqB,CAACC,MAAWC;gBACrC,oCAAoC;gBACpCD,KAAKE,MAAM,CAAC;YACd;YAEA,MAAMC,iBACJZ,gBAAO,CAACa,UAAU,CAACC,uBAAuB,CAACC,mBAAmB,CAC5DzG;YAEJsG,eAAeI,SAAS,CAACV,GAAG,CAACH,YAAY,CAACc,OAAOR;gBAC/C,IAAI,CAACQ,MAAMC,UAAU,IAAI;gBACzB,OAAOV,mBAAmBC,MAAMQ;YAClC;YAEA3G,YAAY8F,KAAK,CAACe,aAAa,CAACC,UAAU,CACxC;gBACElG,MAAMiF;gBACNkB,OAAOrB,gBAAO,CAACsB,WAAW,CAACC,kCAAkC;YAC/D,GACA,CAAChH,SACC,IAAI,CAACH,QAAQ,CACXC,UACAC,aACAC,QACA;oBACEiD;gBACF,GACA/C,OACA;oBAAEC;oBAAiBC;gBAAU;YAInCL,YAAY8F,KAAK,CAACoB,YAAY,CAAClB,GAAG,CAACH,YAAY,CAACsB;gBAC9CA,MAAMrB,KAAK,CAACsB,KAAK,CACdC,GAAG,CAAC,wBACJrB,GAAG,CACF,yBACA,CAAC/D,WAAgB,EAAEqF,KAAK,EAAEC,UAAU,EAAO,GACzC,wCAAwC;oBACxCtF,YAAYqF,MAAMC,WAAW,gBAAgB9F;YAErD;QACF;IACF;AACF"}