{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/wellknown-errors-plugin/parseCss.ts"], "names": ["getCssError", "regexCssError", "fileName", "err", "name", "stack", "SyntaxError", "res", "exec", "message", "_lineNumber", "_column", "reason", "lineNumber", "Math", "max", "parseInt", "column", "SimpleWebpackError", "cyan", "yellow", "toString", "red", "bold", "concat"], "mappings": ";;;;+BAMgBA;;;eAAAA;;;4BANwB;oCACL;AAEnC,MAAMC,gBACJ;AAEK,SAASD,YACdE,QAAgB,EAChBC,GAAU;IAEV,IACE,CACE,CAAA,AAACA,CAAAA,IAAIC,IAAI,KAAK,oBAAoBD,IAAIC,IAAI,KAAK,aAAY,KAC3D,AAACD,IAAYE,KAAK,KAAK,SACvB,CAAEF,CAAAA,eAAeG,WAAU,CAAC,GAE9B;QACA,OAAO;IACT;IAEA,uGAAuG;IAEvG,MAAMC,MAAMN,cAAcO,IAAI,CAACL,IAAIM,OAAO;IAC1C,IAAIF,KAAK;QACP,MAAM,GAAGG,aAAaC,SAASC,OAAO,GAAGL;QACzC,MAAMM,aAAaC,KAAKC,GAAG,CAAC,GAAGC,SAASN,aAAa;QACrD,MAAMO,SAASH,KAAKC,GAAG,CAAC,GAAGC,SAASL,SAAS;QAE7C,OAAO,IAAIO,sCAAkB,CAC3B,CAAC,EAAEC,IAAAA,gBAAI,EAACjB,UAAU,CAAC,EAAEkB,IAAAA,kBAAM,EAACP,WAAWQ,QAAQ,IAAI,CAAC,EAAED,IAAAA,kBAAM,EAC1DH,OAAOI,QAAQ,IACf,CAAC,EACHC,IAAAA,eAAG,EAACC,IAAAA,gBAAI,EAAC,iBAAiBC,MAAM,CAAC,CAAC,EAAE,EAAEZ,OAAO,CAAC;IAElD;IAEA,OAAO;AACT"}