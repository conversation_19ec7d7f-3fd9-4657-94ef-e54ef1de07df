import mongoose, { Schema, Document } from 'mongoose';

export interface IProduct extends Document {
  _id: mongoose.Types.ObjectId;
  name: string;
  description: string;
  price: number;
  images: string[];
  category: string;
  tags: string[];
  inventory: number;
  featured: boolean;
  era?: string;
  condition?: string;
  size?: string;
  material?: string;
  createdAt: Date;
  updatedAt: Date;
}

const ProductSchema = new Schema<IProduct>({
  name: { type: String, required: true },
  description: { type: String, required: true },
  price: { type: Number, required: true },
  images: [String],
  category: { type: String, required: true },
  tags: [String],
  inventory: { type: Number, default: 0 },
  featured: { type: Boolean, default: false },
  era: { type: String }, // e.g., "1950s", "1960s", etc.
  condition: { type: String }, // e.g., "Excellent", "Good", "Fair"
  size: { type: String }, // e.g., "S", "M", "L", "XL"
  material: { type: String }, // e.g., "Cotton", "Silk", "Wool"
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
});

// Update the updatedAt field before saving
ProductSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Create indexes for better performance
ProductSchema.index({ category: 1 });
ProductSchema.index({ featured: 1 });
ProductSchema.index({ era: 1 });
ProductSchema.index({ price: 1 });
ProductSchema.index({ createdAt: -1 });

export default mongoose.models.Product ||
  mongoose.model<IProduct>('Product', ProductSchema);