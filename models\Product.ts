import mongoose, { Schema } from 'mongoose';

const ProductSchema = new Schema({
  name: { type: String, required: true },
  description: { type: String, required: true },
  price: { type: Number, required: true },
  images: [String],
  category: { type: String, required: true },
  tags: [String],
  inventory: { type: Number, default: 0 },
  featured: { type: Boolean, default: false },
  era: { type: String }, // e.g., "1950s", "1960s", etc.
  condition: { type: String }, // e.g., "Excellent", "Good", "Fair"
  size: { type: String }, // e.g., "S", "M", "L", "XL"
  material: { type: String }, // e.g., "Cotton", "Silk", "Wool"
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
});

export default mongoose.models.Product || 
  mongoose.model('Product', ProductSchema);