import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { redirect } from 'next/navigation';

export interface SessionUser {
  id: string;
  email: string;
  name: string;
  isAdmin: boolean;
}

export interface AuthSession {
  user: SessionUser;
  expires: string;
}

/**
 * Verify user session and return session data
 * Returns null if no valid session exists
 */
export async function verifySession(): Promise<AuthSession | null> {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return null;
    }

    return session as AuthSession;
  } catch (error) {
    console.error('Session verification error:', error);
    return null;
  }
}

/**
 * Verify admin session and redirect if not authorized
 * Throws redirect if user is not authenticated or not admin
 */
export async function verifyAdminSession(): Promise<AuthSession> {
  const session = await verifySession();
  
  if (!session) {
    redirect('/api/auth/signin?callbackUrl=/admin');
  }
  
  if (!session.user.isAdmin) {
    redirect('/unauthorized');
  }
  
  return session;
}

/**
 * Check if user is authenticated
 */
export async function isAuthenticated(): Promise<boolean> {
  const session = await verifySession();
  return !!session;
}

/**
 * Check if user is admin
 */
export async function isAdmin(): Promise<boolean> {
  const session = await verifySession();
  return !!(session && session.user.isAdmin);
}
