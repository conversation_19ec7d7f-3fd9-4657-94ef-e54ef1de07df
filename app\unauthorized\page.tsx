import Link from 'next/link';

export default function UnauthorizedPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-cream-50">
      <div className="max-w-md w-full bg-white p-8 rounded-sm shadow-lg text-center">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-sepia-900 mb-2">Access Denied</h1>
          <div className="vintage-divider w-24 mx-auto mb-4"></div>
          <p className="text-sepia-700">
            You don&apos;t have permission to access this page. Admin privileges are required.
          </p>
        </div>
        
        <div className="space-y-4">
          <Link href="/" className="btn-primary inline-block">
            Return Home
          </Link>
          <div>
            <Link href="/api/auth/signin" className="btn-secondary inline-block">
              Sign In as Admin
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
