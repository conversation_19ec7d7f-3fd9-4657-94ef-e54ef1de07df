{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-barrel-loader.ts"], "names": ["barrelTransformMappingCache", "Map", "getBarrelMapping", "layer", "resourcePath", "swcCacheDir", "resolve", "fs", "has", "get", "transpileSource", "filename", "source", "isWildcard", "isTypeScript", "endsWith", "Promise", "res", "transform", "inputSourceMap", "undefined", "sourceFileName", "optimizeBarrelExports", "wildcard", "serverComponents", "isReactServerLayer", "WEBPACK_LAYERS", "reactServerComponents", "jsc", "parser", "syntax", "experimental", "cacheRoot", "then", "output", "code", "visited", "Set", "getMatches", "file", "add", "rej", "readFile", "err", "data", "toString", "matches", "match", "prefix", "exportList", "JSON", "parse", "slice", "wildcardExports", "matchAll", "map", "decl", "length", "all", "req", "targetPath", "path", "dirname", "replace", "targetMatches", "concat", "set", "NextBarrelLoader", "async", "cacheable", "names", "getOptions", "getResolve", "mainFields", "mapping", "_module", "clearDependencies", "callback", "stringify", "exportMap", "name", "filePath", "orig", "missedNames", "push", "join"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAoFC;;;;+BA0OD;;;eAAA;;;6DAtOiB;qBACS;2BACK;;;;;;AAE/B,iFAAiF;AACjF,mFAAmF;AACnF,+DAA+D;AAC/D,kEAAkE;AAClE,MAAMA,8BAA8B,IAAIC;AASxC,eAAeC,iBACbC,KAAgC,EAChCC,YAAoB,EACpBC,WAAmB,EACnBC,OAA8D,EAC9DC,EAKC;IAED,IAAIP,4BAA4BQ,GAAG,CAACJ,eAAe;QACjD,OAAOJ,4BAA4BS,GAAG,CAACL;IACzC;IAEA,6EAA6E;IAC7E,mDAAmD;IACnD,eAAeM,gBACbC,QAAgB,EAChBC,MAAc,EACdC,UAAmB;QAEnB,MAAMC,eAAeH,SAASI,QAAQ,CAAC,UAAUJ,SAASI,QAAQ,CAAC;QACnE,OAAO,IAAIC,QAAgB,CAACC,MAC1BC,IAAAA,cAAS,EAACN,QAAQ;gBAChBD;gBACAQ,gBAAgBC;gBAChBC,gBAAgBV;gBAChBW,uBAAuB;oBACrBC,UAAUV;gBACZ;gBACAW,kBAAkB;oBAChBC,oBAAoBtB,UAAUuB,yBAAc,CAACC,qBAAqB;gBACpE;gBACAC,KAAK;oBACHC,QAAQ;wBACNC,QAAQhB,eAAe,eAAe;wBACtC,CAACA,eAAe,QAAQ,MAAM,EAAE;oBAClC;oBACAiB,cAAc;wBACZC,WAAW3B;oBACb;gBACF;YACF,GAAG4B,IAAI,CAAC,CAACC;gBACPjB,IAAIiB,OAAOC,IAAI;YACjB;IAEJ;IAEA,yCAAyC;IACzC,MAAMC,UAAU,IAAIC;IACpB,eAAeC,WAAWC,IAAY,EAAE1B,UAAmB;QACzD,IAAIuB,QAAQ5B,GAAG,CAAC+B,OAAO;YACrB,OAAO;QACT;QACAH,QAAQI,GAAG,CAACD;QAEZ,MAAM3B,SAAS,MAAM,IAAII,QAAgB,CAACC,KAAKwB;YAC7ClC,GAAGmC,QAAQ,CAACH,MAAM,CAACI,KAAKC;gBACtB,IAAID,OAAOC,SAASxB,WAAW;oBAC7BqB,IAAIE;gBACN,OAAO;oBACL1B,IAAI2B,KAAKC,QAAQ;gBACnB;YACF;QACF;QAEA,MAAMX,SAAS,MAAMxB,gBAAgB6B,MAAM3B,QAAQC;QAEnD,MAAMiC,UAAUZ,OAAOa,KAAK,CAC1B;QAEF,IAAI,CAACD,SAAS;YACZ,OAAO;QACT;QAEA,MAAME,SAASF,OAAO,CAAC,EAAE;QACzB,IAAIG,aAAaC,KAAKC,KAAK,CAACL,OAAO,CAAC,EAAE,CAACM,KAAK,CAAC,GAAG,CAAC;QAKjD,MAAMC,kBAAkB;eACnBnB,OAAOoB,QAAQ,CAAC;SACpB,CAACC,GAAG,CAAC,CAACR,QAAUA,KAAK,CAAC,EAAE;QAEzB,uEAAuE;QACvE,sEAAsE;QACtE,eAAe;QACf,IAAIlC,YAAY;YACd,KAAK,MAAM2C,QAAQP,WAAY;gBAC7BO,IAAI,CAAC,EAAE,GAAGjB;gBACViB,IAAI,CAAC,EAAE,GAAGA,IAAI,CAAC,EAAE;YACnB;QACF;QAEA,6EAA6E;QAC7E,IAAIH,gBAAgBI,MAAM,EAAE;YAC1B,MAAMzC,QAAQ0C,GAAG,CACfL,gBAAgBE,GAAG,CAAC,OAAOI;gBACzB,MAAMC,aAAa,MAAMtD,QACvBuD,aAAI,CAACC,OAAO,CAACvB,OACboB,IAAII,OAAO,CAAC,gDAAgD;gBAG9D,MAAMC,gBAAgB,MAAM1B,WAAWsB,YAAY;gBACnD,IAAII,eAAe;oBACjB,wBAAwB;oBACxBf,aAAaA,WAAWgB,MAAM,CAACD,cAAcf,UAAU;gBACzD;YACF;QAEJ;QAEA,OAAO;YACLD;YACAC;YACAI;QACF;IACF;IAEA,MAAMpC,MAAM,MAAMqB,WAAWlC,cAAc;IAC3CJ,4BAA4BkE,GAAG,CAAC9D,cAAca;IAE9C,OAAOA;AACT;AAEA,MAAMkD,mBAAmB;QAkBrB;IAZF,IAAI,CAACC,KAAK;IACV,IAAI,CAACC,SAAS,CAAC;IAEf,MAAM,EAAEC,KAAK,EAAEjE,WAAW,EAAE,GAAG,IAAI,CAACkE,UAAU;IAE9C,yEAAyE;IACzE,6EAA6E;IAC7E,MAAMjE,UAAU,IAAI,CAACkE,UAAU,CAAC;QAC9BC,YAAY;YAAC;YAAU;SAAO;IAChC;IAEA,MAAMC,UAAU,MAAMxE,kBACpB,gBAAA,IAAI,CAACyE,OAAO,qBAAZ,cAAcxE,KAAK,EACnB,IAAI,CAACC,YAAY,EACjBC,aACAC,SACA,IAAI,CAACC,EAAE;IAGT,4EAA4E;IAC5E,yEAAyE;IACzE,6EAA6E;IAC7E,wBAAwB;IACxB,IAAI,CAACqE,iBAAiB;IAEtB,IAAI,CAACF,SAAS;QACZ,6FAA6F;QAC7F,2FAA2F;QAC3F,0FAA0F;QAC1F,+BAA+B;QAC/B,IAAI,CAACG,QAAQ,CAAC,MAAM,CAAC,cAAc,EAAE3B,KAAK4B,SAAS,CAAC,IAAI,CAAC1E,YAAY,EAAE,CAAC;QACxE;IACF;IAEA,6EAA6E;IAC7E,MAAM4C,SAAS0B,QAAQ1B,MAAM;IAC7B,MAAMC,aAAayB,QAAQzB,UAAU;IACrC,MAAM8B,YAAY,IAAI9E;IACtB,KAAK,MAAM,CAAC+E,MAAMC,UAAUC,KAAK,IAAIjC,WAAY;QAC/C8B,UAAUb,GAAG,CAACc,MAAM;YAACC;YAAUC;SAAK;IACtC;IAEA,IAAIhD,SAASc;IACb,IAAImC,cAAwB,EAAE;IAC9B,KAAK,MAAMH,QAAQV,MAAO;QACxB,sBAAsB;QACtB,IAAIS,UAAUvE,GAAG,CAACwE,OAAO;YACvB,MAAMxB,OAAOuB,UAAUtE,GAAG,CAACuE;YAE3B,IAAIxB,IAAI,CAAC,EAAE,KAAK,KAAK;gBACnBtB,UAAU,CAAC,cAAc,EAAE8C,KAAK,MAAM,EAAE9B,KAAK4B,SAAS,CAACtB,IAAI,CAAC,EAAE,EAAE,CAAC;YACnE,OAAO,IAAIA,IAAI,CAAC,EAAE,KAAK,WAAW;gBAChCtB,UAAU,CAAC,sBAAsB,EAAE8C,KAAK,QAAQ,EAAE9B,KAAK4B,SAAS,CAC9DtB,IAAI,CAAC,EAAE,EACP,CAAC;YACL,OAAO,IAAIA,IAAI,CAAC,EAAE,KAAKwB,MAAM;gBAC3B9C,UAAU,CAAC,WAAW,EAAE8C,KAAK,QAAQ,EAAE9B,KAAK4B,SAAS,CAACtB,IAAI,CAAC,EAAE,EAAE,CAAC;YAClE,OAAO;gBACLtB,UAAU,CAAC,WAAW,EAAEsB,IAAI,CAAC,EAAE,CAAC,IAAI,EAAEwB,KAAK,QAAQ,EAAE9B,KAAK4B,SAAS,CACjEtB,IAAI,CAAC,EAAE,EACP,CAAC;YACL;QACF,OAAO;YACL2B,YAAYC,IAAI,CAACJ;QACnB;IACF;IAEA,mCAAmC;IACnC,IAAIG,YAAY1B,MAAM,GAAG,GAAG;QAC1B,KAAK,MAAME,OAAOe,QAAQrB,eAAe,CAAE;YACzCnB,UAAU,CAAC,gBAAgB,EAAEgB,KAAK4B,SAAS,CACzCnB,IAAII,OAAO,CAAC,mBAAmBoB,YAAYE,IAAI,CAAC,OAAO,cACvD,CAAC;QACL;IACF;IAEA,IAAI,CAACR,QAAQ,CAAC,MAAM3C;AACtB;MAEA,WAAeiC"}