{"version": 3, "sources": ["../../../../src/build/webpack/plugins/css-minimizer-plugin.ts"], "names": ["CssMinimizerPlugin", "CSS_REGEX", "constructor", "options", "__next_css_remove", "optimizeAsset", "file", "asset", "postcssOptions", "to", "from", "parser", "postcssScss", "input", "map", "sourceAndMap", "source", "prev", "postcss", "cssnanoSimple", "process", "then", "res", "sources", "SourceMapSource", "css", "toJSON", "RawSource", "apply", "compiler", "hooks", "compilation", "tap", "cache", "getCache", "processAssets", "tapPromise", "name", "stage", "webpack", "Compilation", "PROCESS_ASSETS_STAGE_OPTIMIZE_SIZE", "assets", "compilationSpan", "spans", "get", "cssMinimizerSpan", "<PERSON><PERSON><PERSON><PERSON>", "setAttribute", "traceAsyncFn", "files", "Object", "keys", "Promise", "all", "filter", "test", "assetSpan", "etag", "getLazyHashedEtag", "cachedResult", "getPromise", "result", "storePromise"], "mappings": ";;;;+BAgBaA;;;eAAAA;;;sEAhBa;oEACF;gEACJ;yBAEa;iCACX;;;;;;AAEtB,4HAA4H;AAC5H,MAAMC,YAAY;AAQX,MAAMD;IAKXE,YAAYC,OAAkC,CAAE;aAJhDC,oBAAoB;QAKlB,IAAI,CAACD,OAAO,GAAGA;IACjB;IAEAE,cAAcC,IAAY,EAAEC,KAAU,EAAE;QACtC,MAAMC,iBAAiB;YACrB,GAAG,IAAI,CAACL,OAAO,CAACK,cAAc;YAC9BC,IAAIH;YACJI,MAAMJ;YAEN,yEAAyE;YACzE,8CAA8C;YAC9C,8FAA8F;YAC9FK,QAAQC,oBAAW;QACrB;QAEA,IAAIC;QACJ,IAAIL,eAAeM,GAAG,IAAIP,MAAMQ,YAAY,EAAE;YAC5C,MAAM,EAAEC,MAAM,EAAEF,GAAG,EAAE,GAAGP,MAAMQ,YAAY;YAC1CF,QAAQG;YACRR,eAAeM,GAAG,CAACG,IAAI,GAAGH,MAAMA,MAAM;QACxC,OAAO;YACLD,QAAQN,MAAMS,MAAM;QACtB;QAEA,OAAOE,IAAAA,gBAAO,EAAC;YAACC,IAAAA,sBAAa,EAAC,CAAC,GAAGD,gBAAO;SAAE,EACxCE,OAAO,CAACP,OAAOL,gBACfa,IAAI,CAAC,CAACC;YACL,IAAIA,IAAIR,GAAG,EAAE;gBACX,OAAO,IAAIS,gBAAO,CAACC,eAAe,CAACF,IAAIG,GAAG,EAAEnB,MAAMgB,IAAIR,GAAG,CAACY,MAAM;YAClE,OAAO;gBACL,OAAO,IAAIH,gBAAO,CAACI,SAAS,CAACL,IAAIG,GAAG;YACtC;QACF;IACJ;IAEAG,MAAMC,QAA0B,EAAE;QAChCA,SAASC,KAAK,CAACC,WAAW,CAACC,GAAG,CAAC,sBAAsB,CAACD;YACpD,MAAME,QAAQF,YAAYG,QAAQ,CAAC;YACnCH,YAAYD,KAAK,CAACK,aAAa,CAACC,UAAU,CACxC;gBACEC,MAAM;gBACNC,OAAOC,gBAAO,CAACC,WAAW,CAACC,kCAAkC;YAC/D,GACA,OAAOC;gBACL,MAAMC,kBAAkBC,sBAAK,CAACC,GAAG,CAACd,gBAAgBa,sBAAK,CAACC,GAAG,CAAChB;gBAC5D,MAAMiB,mBAAmBH,gBAAiBI,UAAU,CAClD;gBAEFD,iBAAiBE,YAAY,CAAC,kBAAkB;gBAEhD,OAAOF,iBAAiBG,YAAY,CAAC;oBACnC,MAAMC,QAAQC,OAAOC,IAAI,CAACV;oBAC1B,MAAMW,QAAQC,GAAG,CACfJ,MACGK,MAAM,CAAC,CAACjD,OAASL,UAAUuD,IAAI,CAAClD,OAChCQ,GAAG,CAAC,OAAOR;wBACV,MAAMmD,YAAYX,iBAAiBC,UAAU,CAAC;wBAC9CU,UAAUT,YAAY,CAAC,QAAQ1C;wBAE/B,OAAOmD,UAAUR,YAAY,CAAC;4BAC5B,MAAM1C,QAAQmC,MAAM,CAACpC,KAAK;4BAE1B,MAAMoD,OAAOzB,MAAM0B,iBAAiB,CAACpD;4BAErC,MAAMqD,eAAe,MAAM3B,MAAM4B,UAAU,CAACvD,MAAMoD;4BAElDD,UAAUT,YAAY,CACpB,SACAY,eAAe,QAAQ;4BAEzB,IAAIA,cAAc;gCAChBlB,MAAM,CAACpC,KAAK,GAAGsD;gCACf;4BACF;4BAEA,MAAME,SAAS,MAAM,IAAI,CAACzD,aAAa,CAACC,MAAMC;4BAC9C,MAAM0B,MAAM8B,YAAY,CAACzD,MAAMoD,MAAMI;4BACrCpB,MAAM,CAACpC,KAAK,GAAGwD;wBACjB;oBACF;gBAEN;YACF;QAEJ;IACF;AACF"}