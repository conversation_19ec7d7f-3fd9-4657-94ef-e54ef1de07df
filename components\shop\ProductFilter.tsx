'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { useState } from 'react';

interface FilterOption {
  value: string;
  label: string;
}

export default function ProductFilter() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [category, setCategory] = useState(searchParams.get('category') || '');
  const [sort, setSort] = useState(searchParams.get('sort') || '');
  const [era, setEra] = useState(searchParams.get('era') || '');
  
  // Categories for vintage fashion
  const categories: FilterOption[] = [
    { value: '', label: 'All Categories' },
    { value: 'dresses', label: 'Dresses' },
    { value: 'tops', label: 'Tops' },
    { value: 'bottoms', label: 'Bottoms' },
    { value: 'outerwear', label: 'Outerwear' },
    { value: 'accessories', label: 'Accessories' },
  ];

  // Sort options
  const sortOptions: FilterOption[] = [
    { value: '', label: 'Newest First' },
    { value: 'price-asc', label: 'Price: Low to High' },
    { value: 'price-desc', label: 'Price: High to Low' },
  ];

  // Era options for vintage filtering
  const eraOptions: FilterOption[] = [
    { value: '', label: 'All Eras' },
    { value: '1950s', label: '1950s' },
    { value: '1960s', label: '1960s' },
    { value: '1970s', label: '1970s' },
    { value: '1980s', label: '1980s' },
    { value: '1990s', label: '1990s' },
  ];
  
  // Apply filters
  const applyFilters = () => {
    const params = new URLSearchParams();

    if (category) params.set('category', category);
    if (sort) params.set('sort', sort);
    if (era) params.set('era', era);

    router.push(`/shop?${params.toString()}`);
  };
  
  return (
    <div className="vintage-card p-4">
      <h2 className="text-lg font-medium mb-4 text-sepia-900">Filter Products</h2>

      <div className="mb-4">
        <label className="block text-sm font-medium text-sepia-700 mb-1">
          Category
        </label>
        <select
          value={category}
          onChange={(e) => setCategory(e.target.value)}
          className="vintage-input w-full"
        >
          {categories.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      </div>

      <div className="mb-4">
        <label className="block text-sm font-medium text-sepia-700 mb-1">
          Era
        </label>
        <select
          value={era}
          onChange={(e) => setEra(e.target.value)}
          className="vintage-input w-full"
        >
          {eraOptions.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      </div>

      <div className="mb-4">
        <label className="block text-sm font-medium text-sepia-700 mb-1">
          Sort By
        </label>
        <select
          value={sort}
          onChange={(e) => setSort(e.target.value)}
          className="vintage-input w-full"
        >
          {sortOptions.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      </div>

      <button
        onClick={applyFilters}
        className="btn-primary w-full"
      >
        Apply Filters
      </button>
    </div>
  );
}