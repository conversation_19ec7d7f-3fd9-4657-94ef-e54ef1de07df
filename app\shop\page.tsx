import { connectToDatabase } from '@/lib/mongodb';
import Product, { IProduct } from '@/models/Product';
import Link from 'next/link';
import Image from 'next/image';
import ProductFilter from '@/components/shop/ProductFilter';

interface ShopPageProps {
  searchParams: {
    category?: string;
    sort?: string;
    page?: string;
    era?: string;
  };
}

interface ProductCardProps {
  product: IProduct;
}

export default async function ShopPage({ searchParams }: ShopPageProps) {
  await connectToDatabase();
  
  const page = parseInt(searchParams.page || '1');
  const pageSize = 12;
  const skip = (page - 1) * pageSize;
  
  // Build query based on filters
  const query: Record<string, any> = {};
  if (searchParams.category) {
    query.category = searchParams.category;
  }
  if (searchParams.era) {
    query.era = searchParams.era;
  }
  
  // Determine sort order
  let sortOptions: Record<string, 1 | -1> = {};
  switch (searchParams.sort) {
    case 'price-asc':
      sortOptions = { price: 1 };
      break;
    case 'price-desc':
      sortOptions = { price: -1 };
      break;
    default:
      sortOptions = { createdAt: -1 };
  }
  
  const products = await Product.find(query)
    .sort(sortOptions)
    .skip(skip)
    .limit(pageSize);
    
  const totalProducts = await Product.countDocuments(query);
  const totalPages = Math.ceil(totalProducts / pageSize);
  
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8 text-center">Vintage Collection</h1>
      
      <div className="flex flex-col md:flex-row gap-8">
        <div className="w-full md:w-1/4">
          <ProductFilter />
        </div>
        
        <div className="w-full md:w-3/4">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {products.map((product) => (
              <ProductCard key={product._id.toString()} product={product} />
            ))}
          </div>
          
          {/* Pagination */}
          {totalPages > 1 && (
            <div className="mt-8 flex justify-center">
              <div className="flex space-x-2">
                {Array.from({ length: totalPages }, (_, i) => i + 1).map((pageNum) => (
                  <Link
                    key={pageNum}
                    href={{
                      pathname: '/shop',
                      query: { ...searchParams, page: pageNum.toString() },
                    }}
                    className={`px-4 py-2 rounded-sm transition-colors ${
                      page === pageNum
                        ? 'bg-rust-600 text-white'
                        : 'bg-cream-200 text-sepia-800 hover:bg-cream-300'
                    }`}
                  >
                    {pageNum}
                  </Link>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

function ProductCard({ product }: ProductCardProps) {
  return (
    <Link href={`/shop/products/${product._id}`}>
      <div className="vintage-card group">
        <div className="relative h-64 w-full overflow-hidden">
          {product.images && product.images[0] ? (
            <Image
              src={product.images[0]}
              alt={product.name}
              fill
              className="object-cover group-hover:scale-105 transition-transform duration-500"
            />
          ) : (
            <div className="w-full h-full bg-cream-200 flex items-center justify-center">
              <span className="text-sepia-500">No Image</span>
            </div>
          )}
          {product.inventory <= 3 && product.inventory > 0 && (
            <div className="absolute top-2 right-2 bg-rust-600 text-white text-xs px-2 py-1 rounded">
              Only {product.inventory} left
            </div>
          )}
        </div>
        <div className="p-4">
          <h3 className="text-lg font-medium text-sepia-900">{product.name}</h3>
          <p className="text-rust-800 font-semibold mt-1">${product.price.toFixed(2)}</p>
          <div className="mt-2 text-sm text-sepia-600">{product.category}</div>
          {product.era && (
            <div className="mt-1 text-xs text-sage-700 bg-sage-100 px-2 py-1 rounded inline-block">
              {product.era}
            </div>
          )}
        </div>
      </div>
    </Link>
  );
}