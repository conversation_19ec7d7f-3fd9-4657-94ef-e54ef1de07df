{"version": 3, "sources": ["../../src/bin/next.ts"], "names": ["performance", "mark", "defaultCommand", "args", "arg", "Boolean", "permissive", "console", "log", "process", "env", "__NEXT_VERSION", "exit", "foundCommand", "commands", "_", "Object", "keys", "join", "command", "includes", "push", "forwarded<PERSON>rgs", "slice", "Error", "defaultEnv", "standardEnv", "NODE_ENV", "isNotStandard", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warn", "NON_STANDARD_NODE_ENV", "NEXT_RUNTIME", "NEXT_MANUAL_SIG_HANDLE", "on", "main", "currentArgsSpec", "commandArgs", "validatedArgs", "getValidatedArgs", "dependency", "require", "resolve", "err", "then", "exec"], "mappings": ";;;;;QAEO;6DACc;8DACL;2BACsB;0BACb;6BACG;kCACK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAPjCA,YAAYC,IAAI,CAAC;AASjB,MAAMC,iBAAiB;AACvB,MAAMC,OAAOC,IAAAA,cAAG,EACd;IACE,QAAQ;IACR,aAAaC;IACb,UAAUA;IACV,aAAaA;IAEb,UAAU;IACV,MAAM;IACN,MAAM;AACR,GACA;IACEC,YAAY;AACd;AAGF,8DAA8D;AAC9D,IAAIH,IAAI,CAAC,YAAY,EAAE;IACrBI,QAAQC,GAAG,CAAC,CAAC,SAAS,EAAEC,QAAQC,GAAG,CAACC,cAAc,CAAC,CAAC;IACpDF,QAAQG,IAAI,CAAC;AACf;AAEA,wDAAwD;AACxD,MAAMC,eAAeR,QAAQS,kBAAQ,CAACX,KAAKY,CAAC,CAAC,EAAE,CAAC;AAEhD,+CAA+C;AAC/C,qDAAqD;AACrD,+DAA+D;AAC/D,IAAI,CAACF,gBAAgBV,IAAI,CAAC,SAAS,EAAE;IACnCI,QAAQC,GAAG,CAAC,CAAC;;;;;MAKT,EAAEQ,OAAOC,IAAI,CAACH,kBAAQ,EAAEI,IAAI,CAAC,MAAM;;;;;;;;EAQvC,CAAC;IACDT,QAAQG,IAAI,CAAC;AACf;AAEA,MAAMO,UAAUN,eAAeV,KAAKY,CAAC,CAAC,EAAE,GAAGb;AAE3C,IAAI;IAAC;IAAwB;CAAwB,CAACkB,QAAQ,CAACD,UAAU;IACvEhB,KAAKY,CAAC,CAACM,IAAI,CAAC,gBAAgBF;AAC9B;AAEA,MAAMG,gBAAgBT,eAAeV,KAAKY,CAAC,CAACQ,KAAK,CAAC,KAAKpB,KAAKY,CAAC;AAE7D,IAAIZ,IAAI,CAAC,YAAY,EACnB,MAAM,IAAIqB,MACR,CAAC,mGAAmG,EAAEL,QAAQ,CAAC;AAGnH,2DAA2D;AAC3D,IAAIhB,IAAI,CAAC,SAAS,EAAE;IAClBmB,cAAcD,IAAI,CAAC;AACrB;AAEA,MAAMI,aAAaN,YAAY,QAAQ,gBAAgB;AAEvD,MAAMO,cAAc;IAAC;IAAc;IAAe;CAAO;AAEzD,IAAIjB,QAAQC,GAAG,CAACiB,QAAQ,EAAE;IACxB,MAAMC,gBAAgB,CAACF,YAAYN,QAAQ,CAACX,QAAQC,GAAG,CAACiB,QAAQ;IAChE,MAAME,qBACJpB,QAAQC,GAAG,CAACiB,QAAQ,KAAK,gBACrB;QAAC;QAAS;KAAQ,GAClBlB,QAAQC,GAAG,CAACiB,QAAQ,KAAK,eACzB;QAAC;KAAM,GACP,EAAE;IAER,IAAIC,iBAAiBC,mBAAmBT,QAAQ,CAACD,UAAU;QACzDX,KAAIsB,IAAI,CAACC,gCAAqB;IAChC;AACF;AAEEtB,QAAQC,GAAG,CAASiB,QAAQ,GAAGlB,QAAQC,GAAG,CAACiB,QAAQ,IAAIF;AACvDhB,QAAQC,GAAG,CAASsB,YAAY,GAAG;AAErC,+EAA+E;AAC/E,6DAA6D;AAC7D,IAAI,CAACvB,QAAQC,GAAG,CAACuB,sBAAsB,IAAId,YAAY,OAAO;IAC5DV,QAAQyB,EAAE,CAAC,WAAW,IAAMzB,QAAQG,IAAI,CAAC;IACzCH,QAAQyB,EAAE,CAAC,UAAU,IAAMzB,QAAQG,IAAI,CAAC;AAC1C;AACA,eAAeuB;IACb,MAAMC,kBAAkBC,wBAAW,CAAClB,QAAQ;IAC5C,MAAMmB,gBAAgBC,IAAAA,kCAAgB,EAACH,iBAAiBd;IAExD,KAAK,MAAMkB,cAAc;QAAC;QAAS;KAAY,CAAE;QAC/C,IAAI;YACF,yEAAyE;YACzEC,QAAQC,OAAO,CAACF;QAClB,EAAE,OAAOG,KAAK;YACZpC,QAAQuB,IAAI,CACV,CAAC,YAAY,EAAEU,WAAW,4HAA4H,EAAEA,WAAW,CAAC,CAAC;QAEzK;IACF;IAEA,MAAM1B,kBAAQ,CAACK,QAAQ,GACpByB,IAAI,CAAC,CAACC,OAASA,KAAKP,gBACpBM,IAAI,CAAC;QACJ,IAAIzB,YAAY,WAAWA,YAAY,wBAAwB;YAC7D,yEAAyE;YACzE,8BAA8B;YAC9BV,QAAQG,IAAI,CAAC;QACf;IACF;AACJ;AAEAuB"}