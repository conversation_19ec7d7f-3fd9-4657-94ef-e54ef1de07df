{"version": 3, "sources": ["../../../src/build/swc/options.ts"], "names": ["getParserOptions", "getJestSWCOptions", "getLoaderSWCOptions", "nextDistPath", "regeneratorRuntimePath", "require", "resolve", "filename", "jsConfig", "rest", "isTSFile", "endsWith", "isTypeScript", "enableDecorators", "Boolean", "compilerOptions", "experimentalDecorators", "syntax", "dynamicImport", "decorators", "importAssertions", "getBaseSWCOptions", "jest", "development", "hasReactRefresh", "globalWindow", "modularizeImports", "swcPlugins", "resolvedBaseUrl", "swcCacheDir", "serverComponents", "isReactServerLayer", "parserConfig", "paths", "emitDecoratorMetadata", "useDefineForClassFields", "plugins", "filter", "Array", "isArray", "map", "name", "options", "jsc", "baseUrl", "externalHelpers", "process", "versions", "pnp", "parser", "experimental", "keepImportAttributes", "emitAssertForImportAttributes", "cacheRoot", "transform", "hidden", "legacyDecorator", "decoratorMetadata", "react", "importSource", "jsxImportSource", "emotion", "runtime", "pragma", "pragmaFrag", "throwIfNamespace", "useBuiltins", "refresh", "optimizer", "simplify", "globals", "typeofs", "window", "envs", "NODE_ENV", "regenerator", "importPath", "sourceMaps", "undefined", "removeConsole", "reactRemoveProperties", "Object", "fromEntries", "entries", "mod", "config", "key", "value", "relay", "styledJsx", "getEmotionOptions", "styledComponents", "getStyledComponentsOptions", "serverActions", "enabled", "styledComponentsConfig", "displayName", "emotionConfig", "autoLabel", "sourcemap", "importMap", "labelFormat", "sourceMap", "isServer", "esm", "pagesDir", "baseOptions", "isNextDist", "test", "env", "targets", "node", "module", "type", "disableNextSsg", "disablePageConfig", "appDir", "isPageFile", "optimizeServerReact", "optimizePackageImports", "supportedBrowsers", "relativeFilePathFromRoot", "fontLoaders", "cjsRequireOptimizer", "packages", "transforms", "NextRequest", "NextResponse", "ImageResponse", "userAgentFromString", "userAgent", "optimize_use_state", "autoModularizeImports", "isDevelopment", "isServerCompiler", "length", "target"], "mappings": ";;;;;;;;;;;;;;;;IAcgBA,gBAAgB;eAAhBA;;IAmOAC,iBAAiB;eAAjBA;;IA0DAC,mBAAmB;eAAnBA;;;AApShB,MAAMC,eACJ;AAEF,MAAMC,yBAAyBC,QAAQC,OAAO,CAC5C;AAGK,SAASN,iBAAiB,EAAEO,QAAQ,EAAEC,QAAQ,EAAE,GAAGC,MAAW;QAIjED;IAHF,MAAME,WAAWH,SAASI,QAAQ,CAAC;IACnC,MAAMC,eAAeF,YAAYH,SAASI,QAAQ,CAAC;IACnD,MAAME,mBAAmBC,QACvBN,6BAAAA,4BAAAA,SAAUO,eAAe,qBAAzBP,0BAA2BQ,sBAAsB;IAEnD,OAAO;QACL,GAAGP,IAAI;QACPQ,QAAQL,eAAe,eAAe;QACtCM,eAAe;QACfC,YAAYN;QACZ,qKAAqK;QACrK,CAACD,eAAe,QAAQ,MAAM,EAAE,CAACF;QACjCU,kBAAkB;IACpB;AACF;AAEA,SAASC,kBAAkB,EACzBd,QAAQ,EACRe,IAAI,EACJC,WAAW,EACXC,eAAe,EACfC,YAAY,EACZC,iBAAiB,EACjBC,UAAU,EACVZ,eAAe,EACfa,eAAe,EACfpB,QAAQ,EACRqB,WAAW,EACXC,gBAAgB,EAChBC,kBAAkB,EAenB;QAEevB,2BAEZA,4BAGAA,4BAGAA,4BAoCQA;IA7CV,MAAMwB,eAAehC,iBAAiB;QAAEO;QAAUC;IAAS;IAC3D,MAAMyB,QAAQzB,6BAAAA,4BAAAA,SAAUO,eAAe,qBAAzBP,0BAA2ByB,KAAK;IAC9C,MAAMpB,mBAAmBC,QACvBN,6BAAAA,6BAAAA,SAAUO,eAAe,qBAAzBP,2BAA2BQ,sBAAsB;IAEnD,MAAMkB,wBAAwBpB,QAC5BN,6BAAAA,6BAAAA,SAAUO,eAAe,qBAAzBP,2BAA2B0B,qBAAqB;IAElD,MAAMC,0BAA0BrB,QAC9BN,6BAAAA,6BAAAA,SAAUO,eAAe,qBAAzBP,2BAA2B2B,uBAAuB;IAEpD,MAAMC,UAAU,AAACT,CAAAA,cAAc,EAAE,AAAD,EAC7BU,MAAM,CAACC,MAAMC,OAAO,EACpBC,GAAG,CAAC,CAAC,CAACC,MAAMC,QAAa,GAAK;YAACrC,QAAQC,OAAO,CAACmC;YAAOC;SAAQ;IAEjE,OAAO;QACLC,KAAK;YACH,GAAIf,mBAAmBK,QACnB;gBACEW,SAAShB;gBACTK;YACF,IACA,CAAC,CAAC;YACNY,iBAAiB,CAACC,QAAQC,QAAQ,CAACC,GAAG,IAAI,CAAC1B;YAC3C2B,QAAQjB;YACRkB,cAAc;gBACZC,sBAAsB;gBACtBC,+BAA+B;gBAC/BhB;gBACAiB,WAAWxB;YACb;YACAyB,WAAW;gBACT,sIAAsI;gBACtI,GAAIhC,OACA;oBACEiC,QAAQ;wBACNjC,MAAM;oBACR;gBACF,IACA,CAAC,CAAC;gBACNkC,iBAAiB3C;gBACjB4C,mBAAmBvB;gBACnBC,yBAAyBA;gBACzBuB,OAAO;oBACLC,cACEnD,CAAAA,6BAAAA,6BAAAA,SAAUO,eAAe,qBAAzBP,2BAA2BoD,eAAe,KACzC7C,CAAAA,CAAAA,mCAAAA,gBAAiB8C,OAAO,KAAI,CAAC9B,qBAC1B,mBACA,OAAM;oBACZ+B,SAAS;oBACTC,QAAQ;oBACRC,YAAY;oBACZC,kBAAkB;oBAClB1C,aAAa,CAAC,CAACA;oBACf2C,aAAa;oBACbC,SAAS,CAAC,CAAC3C;gBACb;gBACA4C,WAAW;oBACTC,UAAU;oBACVC,SAAShD,OACL,OACA;wBACEiD,SAAS;4BACPC,QAAQ/C,eAAe,WAAW;wBACpC;wBACAgD,MAAM;4BACJC,UAAUnD,cAAc,kBAAkB;wBAC5C;oBAEF;gBACN;gBACAoD,aAAa;oBACXC,YAAYxE;gBACd;YACF;QACF;QACAyE,YAAYvD,OAAO,WAAWwD;QAC9BC,aAAa,EAAEhE,mCAAAA,gBAAiBgE,aAAa;QAC7C,sDAAsD;QACtD,yDAAyD;QACzDC,uBAAuB1D,OACnB,QACAP,mCAAAA,gBAAiBiE,qBAAqB;QAC1C,wCAAwC;QACxCtD,mBAAmBA,oBACfuD,OAAOC,WAAW,CAChBD,OAAOE,OAAO,CAACzD,mBAAmBc,GAAG,CAAC,CAAC,CAAC4C,KAAKC,OAAO,GAAK;gBACvDD;gBACA;oBACE,GAAGC,MAAM;oBACT/B,WACE,OAAO+B,OAAO/B,SAAS,KAAK,WACxB+B,OAAO/B,SAAS,GAChB2B,OAAOE,OAAO,CAACE,OAAO/B,SAAS,EAAEd,GAAG,CAAC,CAAC,CAAC8C,KAAKC,MAAM,GAAK;4BACrDD;4BACAC;yBACD;gBACT;aACD,KAEHT;QACJU,KAAK,EAAEzE,mCAAAA,gBAAiByE,KAAK;QAC7B,kFAAkF;QAClFC,WAAW,CAAC;QACZ,2GAA2G;QAC3G,GAAI,CAAC1D,sBAAsB;YACzB,mEAAmE;YACnE8B,SAAS6B,kBAAkB3E,mCAAAA,gBAAiB8C,OAAO,EAAEtC;YACrD,mEAAmE;YACnEoE,kBAAkBC,2BAChB7E,mCAAAA,gBAAiB4E,gBAAgB,EACjCpE;QAEJ,CAAC;QACDO,kBACEA,oBAAoB,CAACR,OACjB;YACES,oBAAoB,CAAC,CAACA;QACxB,IACA+C;QACNe,eACE/D,oBAAoB,CAACR,OACjB;YACE,+BAA+B;YAC/B,2BAA2B;YAC3BwE,SAAS;YACT/D,oBAAoB,CAAC,CAACA;QACxB,IACA+C;IACR;AACF;AAEA,SAASc,2BACPG,sBAAoE,EACpExE,WAAgB;IAEhB,IAAI,CAACwE,wBAAwB;QAC3B,OAAO;IACT,OAAO,IAAI,OAAOA,2BAA2B,UAAU;QACrD,OAAO;YACL,GAAGA,sBAAsB;YACzBC,aAAaD,uBAAuBC,WAAW,IAAIlF,QAAQS;QAC7D;IACF,OAAO;QACL,OAAO;YACLyE,aAAalF,QAAQS;QACvB;IACF;AACF;AAEA,SAASmE,kBACPO,aAAkD,EAClD1E,WAAoB;IAEpB,IAAI,CAAC0E,eAAe;QAClB,OAAO;IACT;IACA,IAAIC,YAAY,CAAC,CAAC3E;IAClB,OAAQ,OAAO0E,kBAAkB,YAAYA,cAAcC,SAAS;QAClE,KAAK;YACHA,YAAY;YACZ;QACF,KAAK;YACHA,YAAY;YACZ;QACF,KAAK;QACL;YACE;IACJ;IACA,OAAO;QACLJ,SAAS;QACTI;QACAC,WAAW5E;QACX,GAAI,OAAO0E,kBAAkB,YAAY;YACvCG,WAAWH,cAAcG,SAAS;YAClCC,aAAaJ,cAAcI,WAAW;YACtCF,WAAW5E,eAAe0E,cAAcK,SAAS;QACnD,CAAC;IACH;AACF;AAEO,SAASrG,kBAAkB,EAChCsG,QAAQ,EACRhG,QAAQ,EACRiG,GAAG,EACH9E,iBAAiB,EACjBC,UAAU,EACVZ,eAAe,EACfP,QAAQ,EACRoB,eAAe,EACf6E,QAAQ,EAYT;IACC,IAAIC,cAAcrF,kBAAkB;QAClCd;QACAe,MAAM;QACNC,aAAa;QACbC,iBAAiB;QACjBC,cAAc,CAAC8E;QACf7E;QACAC;QACAZ;QACAP;QACAoB;QACA,oDAAoD;QACpDG,oBAAoB;QACpB,oDAAoD;QACpDD,kBAAkB;IACpB;IAEA,MAAM6E,aAAaxG,aAAayG,IAAI,CAACrG;IAErC,OAAO;QACL,GAAGmG,WAAW;QACdG,KAAK;YACHC,SAAS;gBACP,yCAAyC;gBACzCC,MAAMjE,QAAQC,QAAQ,CAACgE,IAAI;YAC7B;QACF;QACAC,QAAQ;YACNC,MAAMT,OAAO,CAACG,aAAa,QAAQ;QACrC;QACAO,gBAAgB;QAChBC,mBAAmB;QACnBV;IACF;AACF;AAEO,SAASvG,oBAAoB,EAClCK,QAAQ,EACRgB,WAAW,EACXgF,QAAQ,EACRE,QAAQ,EACRW,MAAM,EACNC,UAAU,EACV7F,eAAe,EACfE,iBAAiB,EACjB4F,mBAAmB,EACnBC,sBAAsB,EACtB5F,UAAU,EACVZ,eAAe,EACfP,QAAQ,EACRgH,iBAAiB,EACjB3F,WAAW,EACX4F,wBAAwB,EACxB3F,gBAAgB,EAChBC,kBAAkB,EAwBnB;IACC,IAAI2E,cAAmBrF,kBAAkB;QACvCd;QACAgB;QACAE,cAAc,CAAC8E;QACf/E;QACAE;QACAC;QACAZ;QACAP;QACA,mBAAmB;QACnBqB;QACAE;QACAD;IACF;IACA4E,YAAYgB,WAAW,GAAG;QACxBA,aAAa;YACX;YACA;YAEA,8CAA8C;YAC9C;YACA;SACD;QACDD;IACF;IACAf,YAAYiB,mBAAmB,GAAG;QAChCC,UAAU;YACR,eAAe;gBACbC,YAAY;oBACVC,aAAa;oBACbC,cAAc;oBACdC,eAAe;oBACfC,qBAAqB;oBACrBC,WAAW;gBACb;YACF;QACF;IACF;IAEA,IAAIZ,uBAAuBf,YAAY,CAAChF,aAAa;QACnDmF,YAAYY,mBAAmB,GAAG;YAChCa,oBAAoB;QACtB;IACF;IAEA,kDAAkD;IAClD,IAAIZ,wBAAwB;QAC1Bb,YAAY0B,qBAAqB,GAAG;YAClCR,UAAUL;QACZ;IACF;IAEA,MAAMZ,aAAaxG,aAAayG,IAAI,CAACrG;IAErC,IAAIgG,UAAU;QACZ,OAAO;YACL,GAAGG,WAAW;YACd,8FAA8F;YAC9FQ,gBAAgB;YAChBC,mBAAmB;YACnBkB,eAAe9G;YACf+G,kBAAkB/B;YAClBE;YACAW;YACAC;YACAR,KAAK;gBACHC,SAAS;oBACP,yCAAyC;oBACzCC,MAAMjE,QAAQC,QAAQ,CAACgE,IAAI;gBAC7B;YACF;QACF;IACF,OAAO;QACL,MAAMrE,UAAU;YACd,GAAGgE,WAAW;YACd,0DAA0D;YAC1D,GAAIC,aACA;gBACEK,QAAQ;oBACNC,MAAM;gBACR;YACF,IACA,CAAC,CAAC;YACNC,gBAAgB,CAACG;YACjBgB,eAAe9G;YACf+G,kBAAkB/B;YAClBE;YACAW;YACAC;YACA,GAAIG,qBAAqBA,kBAAkBe,MAAM,GAAG,IAChD;gBACE1B,KAAK;oBACHC,SAASU;gBACX;YACF,IACA,CAAC,CAAC;QACR;QACA,IAAI,CAAC9E,QAAQmE,GAAG,EAAE;YAChB,6CAA6C;YAC7CnE,QAAQC,GAAG,CAAC6F,MAAM,GAAG;QACvB;QACA,OAAO9F;IACT;AACF"}