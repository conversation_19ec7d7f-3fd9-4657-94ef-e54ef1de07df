{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/resolve-url-loader/index.ts"], "names": ["resolveUrlLoader", "content", "sourceMap", "options", "Object", "assign", "silent", "absolute", "<PERSON><PERSON><PERSON><PERSON>", "root", "debug", "join", "defaultJoin", "getOptions", "sourceMapConsumer", "SourceMapConsumer", "callback", "async", "postcss", "process", "resourcePath", "outputSourceMap", "Boolean", "transformDeclaration", "valueProcessor", "inputSourceMap", "catch", "onFailure", "then", "onSuccess", "error", "encodeError", "reworked", "map", "label", "exception", "Error", "concat", "message", "stack", "split", "trim", "filter"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;AAsBA;;;;+BAMA;;;CAGC,GACD;;;eAA8BA;;;2BARI;uEACP;8BACC;gEACR;;;;;;AAKL,eAAeA,iBAE5B,gBAAgB,GAChBC,OAAe,EACf,mBAAmB,GACnBC,SAAc;IAEd,MAAMC,UAAUC,OAAOC,MAAM,CAC3B;QACEH,WAAW,IAAI,CAACA,SAAS;QACzBI,QAAQ;QACRC,UAAU;QACVC,WAAW;QACXC,MAAM;QACNC,OAAO;QACPC,MAAMC,yBAAW;IACnB,GACA,IAAI,CAACC,UAAU;IAGjB,IAAIC;IACJ,IAAIZ,WAAW;QACbY,oBAAoB,IAAIC,4BAAiB,CAACb;IAC5C;IAEA,MAAMc,WAAW,IAAI,CAACC,KAAK;IAC3B,MAAM,EAAEC,OAAO,EAAE,GAAG,MAAMf,QAAQe,OAAO;IACzCC,IAAAA,gBAAO,EAACD,SAAS,IAAI,CAACE,YAAY,EAAEnB,SAAS;QAC3CoB,iBAAiBC,QAAQnB,QAAQD,SAAS;QAC1CqB,sBAAsBC,IAAAA,uBAAc,EAAC,IAAI,CAACJ,YAAY,EAAEjB;QACxDsB,gBAAgBvB;QAChBY,mBAAmBA;IACrB,EACE,mEAAmE;KAClEY,KAAK,CAACC,UACP,mEAAmE;KAClEC,IAAI,CAACC;IAER,SAASF,UAAUG,KAAY;QAC7B,mEAAmE;QACnEd,SAASe,YAAY,aAAaD;IACpC;IAEA,SAASD,UAAUG,QAAa;QAC9B,IAAIA,UAAU;YACZ,2BAA2B;YAC3B,+DAA+D;YAC/D,IAAI7B,QAAQD,SAAS,EAAE;gBACrBc,SAAS,MAAMgB,SAAS/B,OAAO,EAAE+B,SAASC,GAAG;YAC/C,OAEK;gBACHjB,SAAS,MAAMgB,SAAS/B,OAAO;YACjC;QACF;IACF;IAEA,SAAS8B,YAAYG,KAAU,EAAEC,SAAc;QAC7C,OAAO,IAAIC,MACT;YACE;YACA;YACA;gBAACF;aAAM,CACJG,MAAM,CACL,AAAC,OAAOF,cAAc,YAAYA,aAC/BA,qBAAqBC,SAAS;gBAC7BD,UAAUG,OAAO;gBAChBH,UAAkBI,KAAK,CAACC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,CAACC,IAAI;aAChD,IACD,EAAE,EAELC,MAAM,CAACpB,SACPX,IAAI,CAAC;SACT,CAACA,IAAI,CAAC;IAEX;AACF"}