{"version": 3, "sources": ["../../../../src/build/webpack/plugins/font-stylesheet-gathering-plugin.ts"], "names": ["FontStylesheetGatheringPlugin", "minifyCss", "css", "postcss", "minifier", "excludeAll", "discardComments", "normalizeWhitespace", "exclude", "process", "from", "undefined", "then", "res", "isNodeCreatingLinkElement", "node", "callee", "type", "componentNode", "arguments", "name", "value", "constructor", "adjustFontFallbacks", "adjustFontFallbacksWithSizeAdjust", "gatheredStylesheets", "manifestContent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "factory", "JS_TYPES", "hooks", "parser", "for", "tap", "evaluate", "state", "module", "resource", "includes", "result", "BasicEvaluatedExpression", "setRang<PERSON>", "range", "setExpression", "setIdentifier", "getMembers", "jsxNodeHandler", "length", "arg1", "propsNode", "props", "properties", "for<PERSON>ach", "prop", "key", "rel", "href", "OPTIMIZED_FONT_PROVIDERS", "some", "url", "startsWith", "push", "buildInfo", "valueDependencies", "set", "FONT_MANIFEST", "call", "apply", "compiler", "normalModuleFactory", "make", "tapAsync", "compilation", "cb", "finishModules", "modules", "modulesFinished", "fontStylesheets", "fontUrls", "Set", "fontDependencies", "get", "v", "add", "Array", "fontDefinitionPromises", "map", "getFontDefinitionFromNetwork", "promiseIndex", "getFontOverrideCss", "content", "err", "Log", "warn", "console", "error", "assets", "sources", "RawSource", "JSON", "stringify", "processAssets", "stage", "webpack", "Compilation", "PROCESS_ASSETS_STAGE_ADDITIONS"], "mappings": ";;;;+BAkDaA;;;eAAAA;;;yBA9CN;2BAIA;gEAEa;sEACC;2BAId;6DACc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAErB,SAASC,UAAUC,GAAW;IAC5B,OAAOC,IAAAA,gBAAO,EAAC;QACbC,IAAAA,sBAAQ,EACN;YACEC,YAAY;YACZC,iBAAiB;YACjBC,qBAAqB;gBAAEC,SAAS;YAAM;QACxC,GACAL,gBAAO;KAEV,EACEM,OAAO,CAACP,KAAK;QAAEQ,MAAMC;IAAU,GAC/BC,IAAI,CAAC,CAACC,MAAQA,IAAIX,GAAG;AAC1B;AAEA,SAASY,0BAA0BC,IAAS;IAC1C,MAAMC,SAASD,KAAKC,MAAM;IAC1B,IAAIA,OAAOC,IAAI,KAAK,cAAc;QAChC,OAAO;IACT;IACA,MAAMC,gBAAgBH,KAAKI,SAAS,CAAC,EAAE;IACvC,IAAID,cAAcD,IAAI,KAAK,WAAW;QACpC,OAAO;IACT;IACA,0BAA0B;IAC1B,0BAA0B;IAC1B,OACE,AAACD,CAAAA,OAAOI,IAAI,KAAK,UAAUJ,OAAOI,IAAI,KAAK,OAAM,KACjDF,cAAcG,KAAK,KAAK;AAE5B;AAEO,MAAMrB;IAOXsB,YAAY,EACVC,mBAAmB,EACnBC,iCAAiC,EAIlC,CAAE;aAXHC,sBAAqC,EAAE;aACvCC,kBAAgC,EAAE;aAe1BC,gBAAgB,CACtBC;YAEA,MAAMC,WAAW;gBAAC;gBAAQ;gBAAO;aAAU;YAC3C,uEAAuE;YACvE,KAAK,MAAMZ,QAAQY,SAAU;gBAC3BD,QAAQE,KAAK,CAACC,MAAM,CACjBC,GAAG,CAAC,gBAAgBf,MACpBgB,GAAG,CAAC,IAAI,CAACX,WAAW,CAACF,IAAI,EAAE,CAACW;oBAC3B;;;;;;;;WAQC,GACDA,OAAOD,KAAK,CAACI,QAAQ,CAClBF,GAAG,CAAC,cACJC,GAAG,CAAC,IAAI,CAACX,WAAW,CAACF,IAAI,EAAE,CAACL;4BAEvBgB,sBAAAA;wBADJ,qDAAqD;wBACrD,IAAIA,2BAAAA,gBAAAA,OAAQI,KAAK,sBAAbJ,uBAAAA,cAAeK,MAAM,qBAArBL,qBAAuBM,QAAQ,CAACC,QAAQ,CAAC,iBAAiB;4BAC5D;wBACF;wBACA,IAAIC;wBACJ,IAAIxB,KAAKK,IAAI,KAAK,UAAUL,KAAKK,IAAI,KAAK,SAAS;4BACjDmB,SAAS,IAAIC,iCAAwB;4BACrC,aAAa;4BACbD,OAAOE,QAAQ,CAAC1B,KAAK2B,KAAK;4BAC1BH,OAAOI,aAAa,CAAC5B;4BACrBwB,OAAOK,aAAa,CAAC7B,KAAKK,IAAI;4BAE9B,+BAA+B;4BAC/BmB,OAAOM,UAAU,GAAG,IAAM,EAAE;wBAC9B;wBACA,OAAON;oBACT;oBAEF,MAAMO,iBAAiB,CAAC/B;4BA0CJgB,sBAAAA;wBAzClB,IAAIhB,KAAKI,SAAS,CAAC4B,MAAM,KAAK,GAAG;4BAC/B,uEAAuE;4BACvE;wBACF;wBACA,IAAI,CAACjC,0BAA0BC,OAAO;4BACpC;wBACF;wBAEA,kEAAkE;wBAClE,MAAMiC,OAAOjC,KAAKI,SAAS,CAAC,EAAE;wBAE9B,MAAM8B,YACJD,KAAK/B,IAAI,KAAK,qBAAsB+B,OAAerC;wBACrD,MAAMuC,QAAmC,CAAC;wBAC1C,IAAID,WAAW;4BACbA,UAAUE,UAAU,CAACC,OAAO,CAAC,CAACC;gCAC5B,IAAIA,KAAKpC,IAAI,KAAK,YAAY;oCAC5B;gCACF;gCACA,IACEoC,KAAKC,GAAG,CAACrC,IAAI,KAAK,gBAClBoC,KAAKhC,KAAK,CAACJ,IAAI,KAAK,WACpB;oCACAiC,KAAK,CAACG,KAAKC,GAAG,CAAClC,IAAI,CAAC,GAAGiC,KAAKhC,KAAK,CAACA,KAAK;gCACzC;4BACF;wBACF;wBAEA,IACE,CAAC6B,MAAMK,GAAG,IACVL,MAAMK,GAAG,KAAK,gBACd,CAACL,MAAMM,IAAI,IACX,CAACC,mCAAwB,CAACC,IAAI,CAAC,CAAC,EAAEC,GAAG,EAAE,GACrCT,MAAMM,IAAI,CAACI,UAAU,CAACD,OAExB;4BACA,OAAO;wBACT;wBAEA,IAAI,CAAClC,mBAAmB,CAACoC,IAAI,CAACX,MAAMM,IAAI;wBAExC,MAAMM,YAAY/B,2BAAAA,gBAAAA,OAAQI,KAAK,sBAAbJ,uBAAAA,cAAeK,MAAM,qBAArBL,qBAAuB+B,SAAS;wBAElD,IAAIA,WAAW;4BACbA,UAAUC,iBAAiB,CAACC,GAAG,CAC7BC,wBAAa,EACb,IAAI,CAACxC,mBAAmB;wBAE5B;oBACF;oBAEA,uBAAuB;oBACvBM,OAAOD,KAAK,CAACoC,IAAI,CACdlC,GAAG,CAAC,QACJC,GAAG,CAAC,IAAI,CAACX,WAAW,CAACF,IAAI,EAAE0B;oBAC9B,yBAAyB;oBACzBf,OAAOD,KAAK,CAACoC,IAAI,CACdlC,GAAG,CAAC,SACJC,GAAG,CAAC,IAAI,CAACX,WAAW,CAACF,IAAI,EAAE0B;oBAC9B,2BAA2B;oBAC3Bf,OAAOD,KAAK,CAACoC,IAAI,CACdlC,GAAG,CAAC,gBACJC,GAAG,CAAC,IAAI,CAACX,WAAW,CAACF,IAAI,EAAE0B;gBAChC;YACJ;QACF;QA7GE,IAAI,CAACvB,mBAAmB,GAAGA;QAC3B,IAAI,CAACC,iCAAiC,GAAGA;IAC3C;IA6GO2C,MAAMC,QAA0B,EAAE;QACvC,IAAI,CAACA,QAAQ,GAAGA;QAChBA,SAAStC,KAAK,CAACuC,mBAAmB,CAACpC,GAAG,CACpC,IAAI,CAACX,WAAW,CAACF,IAAI,EACrB,IAAI,CAACO,aAAa;QAEpByC,SAAStC,KAAK,CAACwC,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACjD,WAAW,CAACF,IAAI,EAAE,CAACoD,aAAaC;YAChED,YAAY1C,KAAK,CAAC4C,aAAa,CAACH,QAAQ,CACtC,IAAI,CAACjD,WAAW,CAACF,IAAI,EACrB,OAAOuD,SAAcC;gBACnB,IAAIC,kBAAkB,IAAI,CAACpD,mBAAmB;gBAE9C,MAAMqD,WAAW,IAAIC;gBACrBJ,QAAQvB,OAAO,CAAC,CAAChB;wBAEbA,qCAAAA;oBADF,MAAM4C,mBACJ5C,2BAAAA,oBAAAA,OAAQ0B,SAAS,sBAAjB1B,sCAAAA,kBAAmB2B,iBAAiB,qBAApC3B,oCAAsC6C,GAAG,CAAChB,wBAAa;oBACzD,IAAIe,kBAAkB;wBACpBA,iBAAiB5B,OAAO,CAAC,CAAC8B,IAAcJ,SAASK,GAAG,CAACD;oBACvD;gBACF;gBAEAL,kBAAkBO,MAAM1E,IAAI,CAACoE;gBAE7B,MAAMO,yBAAyBR,gBAAgBS,GAAG,CAAC,CAAC3B,MAClD4B,IAAAA,uCAA4B,EAAC5B;gBAG/B,IAAI,CAACjC,eAAe,GAAG,EAAE;gBACzB,IAAK,IAAI8D,gBAAgBH,uBAAwB;oBAC/C,IAAInF,MAAM,MAAMmF,sBAAsB,CAACG,aAAa;oBAEpD,IAAI,IAAI,CAACjE,mBAAmB,EAAE;wBAC5BrB,OAAOuF,IAAAA,6BAAkB,EACvBZ,eAAe,CAACW,aAAa,EAC7BtF,KACA,IAAI,CAACsB,iCAAiC;oBAE1C;oBAEA,IAAItB,KAAK;wBACP,IAAI;4BACF,MAAMwF,UAAU,MAAMzF,UAAUC;4BAChC,IAAI,CAACwB,eAAe,CAACmC,IAAI,CAAC;gCACxBF,KAAKkB,eAAe,CAACW,aAAa;gCAClCE;4BACF;wBACF,EAAE,OAAOC,KAAK;4BACZC,KAAIC,IAAI,CACN,CAAC,oCAAoC,EAAEhB,eAAe,CAACW,aAAa,CAAC,+BAA+B,CAAC;4BAEvGM,QAAQC,KAAK,CAACJ;wBAChB;oBACF;gBACF;gBAEA,uCAAuC;gBACvCnB,YAAYwB,MAAM,CAAC/B,wBAAa,CAAC,GAAG,IAAIgC,gBAAO,CAACC,SAAS,CACvDC,KAAKC,SAAS,CAAC,IAAI,CAAC1E,eAAe,EAAE,MAAM;gBAG7CkD;YACF;YAEFH;QACF;QAEAL,SAAStC,KAAK,CAACwC,IAAI,CAACrC,GAAG,CAAC,IAAI,CAACX,WAAW,CAACF,IAAI,EAAE,CAACoD;YAC9CA,YAAY1C,KAAK,CAACuE,aAAa,CAACpE,GAAG,CACjC;gBACEb,MAAM,IAAI,CAACE,WAAW,CAACF,IAAI;gBAC3BkF,OAAOC,gBAAO,CAACC,WAAW,CAACC,8BAA8B;YAC3D,GACA,CAACT;gBACCA,MAAM,CAAC,QAAQ/B,wBAAa,CAAC,GAAG,IAAIgC,gBAAO,CAACC,SAAS,CACnDC,KAAKC,SAAS,CAAC,IAAI,CAAC1E,eAAe,EAAE,MAAM;YAE/C;QAEJ;IACF;AACF"}