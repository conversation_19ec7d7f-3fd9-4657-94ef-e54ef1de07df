{"version": 3, "sources": ["../../../src/build/analysis/extract-const-value.ts"], "names": ["NoSuchDeclarationError", "UnsupportedValueError", "extractExportedConstValue", "Error", "isExportDeclaration", "node", "type", "isVariableDeclaration", "isIdentifier", "isBooleanLiteral", "is<PERSON>ull<PERSON>iteral", "isStringLiteral", "isNumericLiteral", "isArrayExpression", "isObjectExpression", "isKeyValueProperty", "isRegExpLiteral", "isTemplateLiteral", "constructor", "message", "paths", "codePath", "path", "extractValue", "value", "RegExp", "pattern", "flags", "undefined", "arr", "i", "len", "elements", "length", "elem", "spread", "push", "expression", "obj", "prop", "properties", "key", "expressions", "cooked", "raw", "quasis", "module", "exportedName", "moduleItem", "body", "declaration", "kind", "decl", "declarations", "id", "init"], "mappings": ";;;;;;;;;;;;;;;;IAiBaA,sBAAsB;eAAtBA;;IAkDAC,qBAAqB;eAArBA;;IAuJGC,yBAAyB;eAAzBA;;;AAzMT,MAAMF,+BAA+BG;AAAO;AAEnD,SAASC,oBAAoBC,IAAU;IACrC,OAAOA,KAAKC,IAAI,KAAK;AACvB;AAEA,SAASC,sBAAsBF,IAAU;IACvC,OAAOA,KAAKC,IAAI,KAAK;AACvB;AAEA,SAASE,aAAaH,IAAU;IAC9B,OAAOA,KAAKC,IAAI,KAAK;AACvB;AAEA,SAASG,iBAAiBJ,IAAU;IAClC,OAAOA,KAAKC,IAAI,KAAK;AACvB;AAEA,SAASI,cAAcL,IAAU;IAC/B,OAAOA,KAAKC,IAAI,KAAK;AACvB;AAEA,SAASK,gBAAgBN,IAAU;IACjC,OAAOA,KAAKC,IAAI,KAAK;AACvB;AAEA,SAASM,iBAAiBP,IAAU;IAClC,OAAOA,KAAKC,IAAI,KAAK;AACvB;AAEA,SAASO,kBAAkBR,IAAU;IACnC,OAAOA,KAAKC,IAAI,KAAK;AACvB;AAEA,SAASQ,mBAAmBT,IAAU;IACpC,OAAOA,KAAKC,IAAI,KAAK;AACvB;AAEA,SAASS,mBAAmBV,IAAU;IACpC,OAAOA,KAAKC,IAAI,KAAK;AACvB;AAEA,SAASU,gBAAgBX,IAAU;IACjC,OAAOA,KAAKC,IAAI,KAAK;AACvB;AAEA,SAASW,kBAAkBZ,IAAU;IACnC,OAAOA,KAAKC,IAAI,KAAK;AACvB;AAEO,MAAML,8BAA8BE;IAIzCe,YAAYC,OAAe,EAAEC,KAAgB,CAAE;QAC7C,KAAK,CAACD;QAEN,8DAA8D;QAC9D,IAAIE;QACJ,IAAID,OAAO;YACTC,WAAW;YACX,KAAK,MAAMC,QAAQF,MAAO;gBACxB,IAAIE,IAAI,CAAC,EAAE,KAAK,KAAK;oBACnB,kBAAkB;oBAClBD,YAAYC;gBACd,OAAO;oBACL,IAAID,aAAa,IAAI;wBACnBA,WAAWC;oBACb,OAAO;wBACL,oBAAoB;wBACpBD,YAAY,CAAC,CAAC,EAAEC,KAAK,CAAC;oBACxB;gBACF;YACF;QACF;QAEA,IAAI,CAACA,IAAI,GAAGD;IACd;AACF;AAEA,SAASE,aAAalB,IAAU,EAAEiB,IAAe;IAC/C,IAAIZ,cAAcL,OAAO;QACvB,OAAO;IACT,OAAO,IAAII,iBAAiBJ,OAAO;QACjC,oBAAoB;QACpB,OAAOA,KAAKmB,KAAK;IACnB,OAAO,IAAIb,gBAAgBN,OAAO;QAChC,aAAa;QACb,OAAOA,KAAKmB,KAAK;IACnB,OAAO,IAAIZ,iBAAiBP,OAAO;QACjC,WAAW;QACX,OAAOA,KAAKmB,KAAK;IACnB,OAAO,IAAIR,gBAAgBX,OAAO;QAChC,cAAc;QACd,OAAO,IAAIoB,OAAOpB,KAAKqB,OAAO,EAAErB,KAAKsB,KAAK;IAC5C,OAAO,IAAInB,aAAaH,OAAO;QAC7B,OAAQA,KAAKmB,KAAK;YAChB,KAAK;gBACH,OAAOI;YACT;gBACE,MAAM,IAAI3B,sBACR,CAAC,oBAAoB,EAAEI,KAAKmB,KAAK,CAAC,CAAC,CAAC,EACpCF;QAEN;IACF,OAAO,IAAIT,kBAAkBR,OAAO;QAClC,iBAAiB;QACjB,MAAMwB,MAAM,EAAE;QACd,IAAK,IAAIC,IAAI,GAAGC,MAAM1B,KAAK2B,QAAQ,CAACC,MAAM,EAAEH,IAAIC,KAAKD,IAAK;YACxD,MAAMI,OAAO7B,KAAK2B,QAAQ,CAACF,EAAE;YAC7B,IAAII,MAAM;gBACR,IAAIA,KAAKC,MAAM,EAAE;oBACf,gBAAgB;oBAChB,MAAM,IAAIlC,sBACR,uDACAqB;gBAEJ;gBAEAO,IAAIO,IAAI,CAACb,aAAaW,KAAKG,UAAU,EAAEf,QAAQ;uBAAIA;oBAAM,CAAC,CAAC,EAAEQ,EAAE,CAAC,CAAC;iBAAC;YACpE,OAAO;gBACL,gBAAgB;gBAChB,aAAa;gBACbD,IAAIO,IAAI,CAACR;YACX;QACF;QACA,OAAOC;IACT,OAAO,IAAIf,mBAAmBT,OAAO;QACnC,sBAAsB;QACtB,MAAMiC,MAAW,CAAC;QAClB,KAAK,MAAMC,QAAQlC,KAAKmC,UAAU,CAAE;YAClC,IAAI,CAACzB,mBAAmBwB,OAAO;gBAC7B,gBAAgB;gBAChB,MAAM,IAAItC,sBACR,wDACAqB;YAEJ;YAEA,IAAImB;YACJ,IAAIjC,aAAa+B,KAAKE,GAAG,GAAG;gBAC1B,sBAAsB;gBACtBA,MAAMF,KAAKE,GAAG,CAACjB,KAAK;YACtB,OAAO,IAAIb,gBAAgB4B,KAAKE,GAAG,GAAG;gBACpC,0BAA0B;gBAC1BA,MAAMF,KAAKE,GAAG,CAACjB,KAAK;YACtB,OAAO;gBACL,MAAM,IAAIvB,sBACR,CAAC,sBAAsB,EAAEsC,KAAKE,GAAG,CAACnC,IAAI,CAAC,0BAA0B,CAAC,EAClEgB;YAEJ;YAEAgB,GAAG,CAACG,IAAI,GAAGlB,aAAagB,KAAKf,KAAK,EAAEF,QAAQ;mBAAIA;gBAAMmB;aAAI;QAC5D;QAEA,OAAOH;IACT,OAAO,IAAIrB,kBAAkBZ,OAAO;QAClC,aAAa;QACb,IAAIA,KAAKqC,WAAW,CAACT,MAAM,KAAK,GAAG;YACjC,sDAAsD;YACtD,MAAM,IAAIhC,sBACR,iDACAqB;QAEJ;QAEA,4EAA4E;QAC5E,2EAA2E;QAC3E,kFAAkF;QAClF,wBAAwB;QACxB,mFAAmF;QACnF,EAAE;QACF,4EAA4E;QAC5E,qEAAqE;QACrE,gGAAgG;QAChG,MAAM,CAAC,EAAEqB,MAAM,EAAEC,GAAG,EAAE,CAAC,GAAGvC,KAAKwC,MAAM;QAErC,OAAOF,UAAUC;IACnB,OAAO;QACL,MAAM,IAAI3C,sBACR,CAAC,uBAAuB,EAAEI,KAAKC,IAAI,CAAC,CAAC,CAAC,EACtCgB;IAEJ;AACF;AAgBO,SAASpB,0BACd4C,OAAc,EACdC,YAAoB;IAEpB,KAAK,MAAMC,cAAcF,QAAOG,IAAI,CAAE;QACpC,IAAI,CAAC7C,oBAAoB4C,aAAa;YACpC;QACF;QAEA,MAAME,cAAcF,WAAWE,WAAW;QAC1C,IAAI,CAAC3C,sBAAsB2C,cAAc;YACvC;QACF;QAEA,IAAIA,YAAYC,IAAI,KAAK,SAAS;YAChC;QACF;QAEA,KAAK,MAAMC,QAAQF,YAAYG,YAAY,CAAE;YAC3C,IACE7C,aAAa4C,KAAKE,EAAE,KACpBF,KAAKE,EAAE,CAAC9B,KAAK,KAAKuB,gBAClBK,KAAKG,IAAI,EACT;gBACA,OAAOhC,aAAa6B,KAAKG,IAAI,EAAE;oBAACR;iBAAa;YAC/C;QACF;IACF;IAEA,MAAM,IAAI/C;AACZ"}